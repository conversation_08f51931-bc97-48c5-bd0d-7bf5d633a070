import { StyledEngineProvider } from '@mui/material/styles'
import { type FC, useMemo, useState } from 'react'

import { LOCAL_STORAGE_THEME_KEY, Theme, ThemeContext } from '../lib/ThemeContext'

const themeLocalStorage = localStorage.getItem(LOCAL_STORAGE_THEME_KEY) as Theme
const isFindThemeList = Object.values(Theme).includes(themeLocalStorage)
const defaultTheme = isFindThemeList ? themeLocalStorage : Theme.LIGHT

const ThemeProvider: FC<any> = ({ children }) => {
  const [theme, setTheme] = useState<Theme>(defaultTheme)

  const defaultProps = useMemo(
    () => ({
      theme,
      setTheme,
    }),
    [theme],
  )

  return (
    <ThemeContext.Provider value={defaultProps}>
      <StyledEngineProvider injectFirst>{children}</StyledEngineProvider>
    </ThemeContext.Provider>
  )
}

export default ThemeProvider

import { useContext } from 'react'

import { LOCAL_STORAGE_THEME_KEY, Theme, ThemeContext } from './ThemeContext'

interface UseThemeResult {
  toggleTheme: (key: Theme) => void
  theme: Theme
}

export function useTheme(): UseThemeResult {
  const { theme, setTheme } = useContext(ThemeContext)
  const toggleTheme = (key: Theme) => {
    setTheme && setTheme(key)
    localStorage.setItem(LOCAL_STORAGE_THEME_KEY, key)
  }

  return { theme: theme ?? Theme['LIGHT'], toggleTheme }
}

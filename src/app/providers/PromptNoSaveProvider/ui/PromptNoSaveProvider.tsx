import { createContext, FC, ReactNode, useEffect, useState } from 'react'

interface PromptContextType {
  promptNoSave: boolean
  togglePromptNoSave: (value: boolean) => void
}

export const PromptProviderContext = createContext<PromptContextType>({
  promptNoSave: false,
  togglePromptNoSave: () => {},
})

export const PromptNoSaveProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [promptNoSave, setPromptNoSave] = useState<boolean>(false)

  const togglePromptNoSave = (value: boolean) => {
    setPromptNoSave(value)
  }

  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      event.preventDefault()
      event.returnValue = 'У вас есть несохраненные изменения. Вы действительно хотите перейти?'

      return true
    }

    if (promptNoSave) {
      window.addEventListener('beforeunload', handleBeforeUnload)
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [promptNoSave])

  return (
    <PromptProviderContext.Provider value={{ promptNoSave, togglePromptNoSave }}>
      {children}
    </PromptProviderContext.Provider>
  )
}

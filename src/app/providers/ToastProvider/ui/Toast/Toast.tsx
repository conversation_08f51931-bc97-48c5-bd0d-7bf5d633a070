import ErrorOutlineOutlinedIcon from '@mui/icons-material/ErrorOutlineOutlined'
import { Alert, AlertColor, AlertTitle, Snackbar } from '@mui/material'
import { IToast } from 'app/providers/ToastProvider/ToastProvider.tsx'
import { FC, useEffect } from 'react'
import { Button } from 'shared/ui/Button'
import { useStore } from 'stores/useStore.ts'

import cls from './Toast.module.scss'

interface ToastProps {
  toast: IToast
}

export const Toast: FC<ToastProps> = (props) => {
  const { notificationStore } = useStore()
  const { deleteNotification } = notificationStore
  const { toast } = props

  useEffect(() => {
    if (toast.type !== 'error' && toast.type !== 'actions') {
      const timeoutId = setTimeout(() => {
        deleteNotification(toast.id)
        clearTimeout(timeoutId)
      }, 6000)
    }
  }, [])

  const alertSeverity = toast.type === 'actions' ? 'warning' : (toast.type as AlertColor)

  return (
    <Snackbar open className={cls.snackbar}>
      <Alert
        severity={alertSeverity}
        icon={toast.type === 'actions' ? <ErrorOutlineOutlinedIcon /> : undefined}
        onClose={() => deleteNotification(toast.id)}
      >
        <AlertTitle>{toast.title}</AlertTitle>
        <div className={cls.descriptionContainer}>
          {toast.multiError ? (
            <div className={cls.multiErrorWrapper}>
              {String(toast.description)
                .split(' ;')
                .map((error) => (
                  <p key={error}>{error}</p>
                ))}
            </div>
          ) : (
            toast.description
          )}
          {toast.type === 'actions' && (
            <div className={cls.buttonContainer}>
              <Button
                className={cls.updateButton}
                onClick={() => {
                  deleteNotification(toast.id)
                  toast.action && toast.action()
                }}
              >
                Обновить
              </Button>
              <Button className={cls.ignoreButton} onClick={() => deleteNotification(toast.id)}>
                Игнорировать
              </Button>
            </div>
          )}
        </div>
      </Alert>
    </Snackbar>
  )
}

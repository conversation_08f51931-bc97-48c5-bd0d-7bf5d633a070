import { AlertColor, IconButton } from '@mui/material'
import { Toast } from 'app/providers/ToastProvider/ui/Toast'
import { observer } from 'mobx-react'
import { type ReactNode, useEffect, useRef } from 'react'
import { Icon } from 'shared/ui/Icon'
import { useStore } from 'stores/useStore.ts'

import cls from './ToastProvider.module.scss'

export interface IToast {
  id: string
  type: AlertColor | undefined | 'actions'
  title: string | ReactNode
  description: string | ReactNode
  action?: () => void
  multiError?: boolean
}

const ToastContextProvider = observer(({ children }: { children: ReactNode }) => {
  const { notificationStore } = useStore()
  const { toastList, deleteNotificationsBasedOnRoute, deleteAllNotifications } = notificationStore

  /**
   * Отслеживаем переход пользователя между страницами
   *
   * Примечание:
   * Не удалось использовать библиотеку react-router-dom, из-за сложившейся композиции компонентов.
   * ToastContextProvider не является дочерним к компоненту <Route />
   */
  const currentRoute = useRef<string>(window.location.pathname)
  useEffect(() => {
    const observer = new MutationObserver(() => {
      const newCurrentRoute = window.location.pathname + window.location.search
      if (currentRoute.current !== newCurrentRoute) {
        currentRoute.current = newCurrentRoute
        deleteNotificationsBasedOnRoute(newCurrentRoute)
      }
    })

    observer.observe(document, { subtree: true, childList: true })

    return () => {
      observer.disconnect()
    }
  }, [])

  return (
    <>
      {children}
      <div className={cls.stackToastWrapper}>
        {toastList.length > 1 && (
          <IconButton className={cls.closeAllButton} onClick={deleteAllNotifications}>
            <Icon name='close' width={24} />
          </IconButton>
        )}
        <div className={cls.stackToast}>
          {[...toastList].reverse().map((toast) => (
            <Toast key={toast.id} toast={toast} />
          ))}
        </div>
      </div>
    </>
  )
})

export default ToastContextProvider

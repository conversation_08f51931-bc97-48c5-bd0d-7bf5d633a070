.stackToast {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: end;
  overflow: auto;
  border-radius: 8px;

  &::-webkit-scrollbar {
    display: none;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  &Wrapper {
    height: 100%;
    position: fixed;
    inset: 0;
    z-index: 1400;
    padding: 16px;

    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: end;
    gap: 4px;
    pointer-events: none;
  }
}

.closeAllButton {
  pointer-events: auto;
  color: #034fad;
}

import { createContext } from 'react'

export interface CollapsedContextProps {
  collapsed: boolean
  setCollapsed: (collapsed: boolean) => void
  isAnimating: boolean
  toggleCollapsed: () => void
}

export const CollapsedProviderContext = createContext<CollapsedContextProps>({
  collapsed: false,
  setCollapsed: () => {},
  isAnimating: false,
  toggleCollapsed: () => {},
})

export const LOCAL_STORAGE_COLLAPSED_KEY = 'collapsed'

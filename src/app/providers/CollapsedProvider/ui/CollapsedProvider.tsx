import { type FC, ReactNode, useCallback, useEffect, useMemo, useState } from 'react'

import { CollapsedProviderContext, LOCAL_STORAGE_COLLAPSED_KEY } from '../lib/CollapsedProviderContext'

interface CollapsedProviderProps {
  children: ReactNode
}

const defaultValue = JSON.parse(localStorage.getItem(LOCAL_STORAGE_COLLAPSED_KEY) as string) ?? false

const CollapsedProvider: FC<CollapsedProviderProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState<boolean>(defaultValue)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    try {
      localStorage.setItem(LOCAL_STORAGE_COLLAPSED_KEY, JSON.stringify(collapsed))
    } catch {
      // Игнорируем ошибки localStorage
    }
  }, [collapsed])

  const toggleCollapsed = useCallback(() => {
    setIsAnimating(true)

    setCollapsed((prev) => !prev)

    setTimeout(() => {
      setIsAnimating(false)
    }, 350) // 300ms анимация SideBar (см. transition в SideBar.module.scss) + 50ms буфер
  }, [])

  const contextValue = useMemo(
    () => ({
      collapsed,
      setCollapsed,
      isAnimating,
      toggleCollapsed,
    }),
    [collapsed, isAnimating, toggleCollapsed],
  )

  return <CollapsedProviderContext.Provider value={contextValue}>{children}</CollapsedProviderContext.Provider>
}

export default CollapsedProvider

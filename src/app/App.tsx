import { useTheme } from 'app/providers/ThemeProvider'
import { observer } from 'mobx-react-lite'
import { Suspense, useEffect } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'
import { PageLoader } from 'shared/ui/PageLoader/PageLoader'

import { AppRouter } from './routes'

const reloadPage = () => {
  const lastReloadTime = localStorage.getItem('preloadError:time')
  if (lastReloadTime && new Date().getTime() - Number(lastReloadTime) < 1000 * 30) {
    localStorage.setItem('vite:preloadError', JSON.stringify(new Date().getTime()))

    return
  }
  localStorage.setItem('vite:preloadError', JSON.stringify(new Date().getTime()))
  window.location.reload()
}

export const App = observer(() => {
  const { theme } = useTheme()

  useEffect(() => {
    window.addEventListener('vite:preloadError', reloadPage)

    return () => {
      window.removeEventListener('vite:preloadError', reloadPage)
      localStorage.removeItem('preloadError:time')
    }
  }, [])

  return (
    <div className={classNames('app', {}, [theme])}>
      <Suspense fallback={<PageLoader />}>
        <AppRouter />
      </Suspense>
    </div>
  )
})

export default App

import { makeAutoObservable } from 'mobx'
import type { RootStore } from 'stores/RootStore.ts'

import { getReport, getReportsByPlantId } from '../api'
import { IReportByPlantStore } from './ReportByPlantStore.types'

export class ReportByPlantStore {
  rootStore: RootStore
  reports: IReportByPlantStore['reports'] = []
  selectedReport: IReportByPlantStore['selectedReport'] = null
  report: IReportByPlantStore['report'] = null
  isReportSelectorModalOpened: IReportByPlantStore['isReportSelectorModalOpened'] = false

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore
    makeAutoObservable(this)
  }

  setSelectedReport: IReportByPlantStore['setSelectedReport'] = (reportId) => {
    this.selectedReport = reportId
  }

  openModalReportsSelector: IReportByPlantStore['openModalReportsSelector'] = async (plantId) => {
    try {
      this.reports = await getReportsByPlantId(plantId)
      if (this.reports.length === 1) {
        this.selectedReport = this.reports[0].id
        await this.initiateReportSubmission()
      } else if (this.reports.length > 0) {
        this.selectedReport = this.reports[0].id
        this.isReportSelectorModalOpened = true
      }
    } catch (error) {
      console.error(error)
    }
  }

  closeModalReportsSelector = () => {
    this.isReportSelectorModalOpened = false
  }

  initiateReportSubmission = async () => {
    if (this.selectedReport === null) return
    try {
      this.report = await getReport(this.selectedReport)
      this.closeModalReportsSelector()
    } catch (error) {
      console.error(error)
    }
  }

  closeModalMailingReport = () => {
    this.report = null
  }
}

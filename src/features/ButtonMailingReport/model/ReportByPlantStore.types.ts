type ReportsByPlantResponse = {
  id: number
  name: string
  description: string
}[]

interface ReportResponse {
  id: number
  reportType: {
    code: string
    title: string
  }
  name: string
  description: string
  settings: {
    fileNameTemplate: string
    emails: string[]
    mailSubject: string
    plants: { id: 15 }[]
    avrcmTes: boolean
    templatePath: string
    firstHourRow: number
    unloadingDirectory: string
    sendFromCalculationPage: boolean
    calculationPagePlants: { id: number }[]
    putDate: boolean
    dateRow: number
    dateColumn: number
    summaryFormat: boolean
    putHeader: boolean
  }
}

export interface IReportByPlantStore {
  reports: ReportsByPlantResponse
  selectedReport: number | null
  report: ReportResponse | null
  isReportSelectorModalOpened: boolean

  setSelectedReport: (reportId: number) => void
  openModalReportsSelector: (plantId: number) => Promise<void>
}

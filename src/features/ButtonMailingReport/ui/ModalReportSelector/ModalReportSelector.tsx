import { FormControlLabel, Radio, RadioGroup } from '@mui/material'
import { observer } from 'mobx-react'
import { Button } from 'shared/ui/Button'
import { Modal } from 'shared/ui/Modal/Modal'
import { useStore } from 'stores/useStore.ts'

import cls from './ModalReportSelector.module.scss'

export const ModalReportSelector = observer(() => {
  const { reportByPlantStore } = useStore()
  const { reports, setSelectedReport, selectedReport, initiateReportSubmission, closeModalReportsSelector } =
    reportByPlantStore

  return (
    <Modal
      open
      maxWidth='sm'
      title='Рассылка отчетов'
      onClose={closeModalReportsSelector}
      actions={
        <div className={cls.actions}>
          <Button onClick={initiateReportSubmission}>Отправить</Button>
        </div>
      }
    >
      <RadioGroup
        className={cls.container}
        value={selectedReport}
        onChange={(e) => setSelectedReport(Number(e.target.value))}
      >
        {reports.map((report) => (
          <FormControlLabel key={report.id} value={report.id} control={<Radio />} label={report.name} />
        ))}
      </RadioGroup>
    </Modal>
  )
})

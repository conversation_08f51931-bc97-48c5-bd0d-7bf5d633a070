import { IReportByPlantStore } from 'features/ButtonMailingReport/model/ReportByPlantStore.types.ts'
import { axiosInstance as api } from 'shared/lib/axios'

export const getReportsByPlantId = (plantId: number): Promise<IReportByPlantStore['reports']> =>
  api.get('/api/v1/reports/plant', { params: { plantId } })

export const getReport = (id: number): Promise<IReportByPlantStore['report']> => api.get(`/api/v1/reports/${id}`)

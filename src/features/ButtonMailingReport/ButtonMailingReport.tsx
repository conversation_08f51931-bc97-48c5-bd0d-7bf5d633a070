import { MessagesWarnings } from 'entities/widgets/Vault.entities.ts'
import { ModalMailingReportAvrchmSummary } from 'features/ModalMailingReportAvrchmSummary/ModalMailingReportAvrchmSummary.tsx'
import { ModalMailingReportHourlyLoadChart } from 'features/ModalMailingReportHourlyLoadChart/ModalMailingReportHourlyLoadChart.tsx'
import { observer } from 'mobx-react'
import { CalculationActionButton } from 'pages/CalculationsPage/ui/CalculationButton/CalculationActionButton.tsx'
import { GAESActionButton } from 'pages/GAESCalculationsPage/ui/GAESMainSection/ui/GAESCalculation/ui/GAESCalculationActionsPanel/ui/GAESActionButton/GAESActionButton'
import { FC, useCallback } from 'react'
import { useLocation } from 'react-router-dom'
import { useStore } from 'stores/useStore.ts'

import { ModalReportSelector } from './ui/ModalReportSelector'

interface ReportButtonProps {
  plantId?: number | null
}

export const ButtonMailingReport: FC<ReportButtonProps> = observer((props) => {
  const { plantId } = props
  const { calculationsPageStore, reportByPlantStore, gaesCalculationsStore } = useStore()
  const location = useLocation()
  const isGAESPage = location.pathname.includes('gaes-calculations')

  // For CalculationsPage
  const { vaultStore, isLastDay, viewOnly, editMode } = calculationsPageStore
  const { isEditRows: calcIsEditRows } = vaultStore

  // For GAESCalculationPage
  const { gaesCalculationTabStore } = gaesCalculationsStore
  const { isEditRows: gaesIsEditRows } = gaesCalculationTabStore

  const { isReportSelectorModalOpened, report, openModalReportsSelector, closeModalMailingReport } = reportByPlantStore

  const handleOpenModalReportsSelector = useCallback(async () => {
    if (typeof plantId === 'number') {
      await openModalReportsSelector(plantId)
    }
  }, [plantId, openModalReportsSelector])

  return (
    <>
      {isGAESPage ? (
        <GAESActionButton
          buttonName={MessagesWarnings.SEND_REPORT}
          isEditRows={gaesIsEditRows}
          onClick={handleOpenModalReportsSelector}
        />
      ) : (
        <CalculationActionButton
          buttonName={MessagesWarnings.SEND_REPORT}
          viewOnly={viewOnly}
          isEditRows={calcIsEditRows}
          isLastDay={isLastDay}
          editMode={editMode}
          onClick={handleOpenModalReportsSelector}
        />
      )}
      {isReportSelectorModalOpened && <ModalReportSelector />}
      {report?.reportType.code === 'AVRCHM_SUMMARY' && (
        <ModalMailingReportAvrchmSummary id={report.id} onClose={closeModalMailingReport} reportType='AVRCHM_SUMMARY' />
      )}
      {report?.reportType.code === 'PLAN_GENERATION' && (
        <ModalMailingReportHourlyLoadChart
          id={report.id}
          onClose={closeModalMailingReport}
          reportType='PLAN_GENERATION'
        />
      )}
    </>
  )
})

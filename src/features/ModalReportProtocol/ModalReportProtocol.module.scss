.ModalProtocol {
  > div > div {
    max-height: 80%;
    max-width: 80%;
    min-width: 600px;
  }
}

.title {
  color: rgb(150, 150, 150);
  font-size: 1.125rem;
  font-weight: 600;
}

.list {
  list-style: none;
  overflow-y: auto;
}

.listItem {
  display: flex;
  align-items: center;
  gap: 1em;
  padding: 0.5em 0;
  border-bottom: 1px solid rgb(150, 150, 150);
}

.icon {
  display: flex;
  align-items: center;

  &Warning {
    color: var(--orange-color);
  }
  &Error {
    color: var(--red-color);
  }
}

.modalFooterRight {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1em;
}

.Modal > div > div {
  width: 600px;
}

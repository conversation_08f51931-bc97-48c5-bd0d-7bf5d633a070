import CloseRoundedIcon from '@mui/icons-material/CloseRounded'
import PriorityHighIcon from '@mui/icons-material/PriorityHigh'
import { useMemo } from 'react'
import { generateUUID } from 'shared/lib/GenerationUUID'
import { Button } from 'shared/ui/Button'
import { Modal } from 'shared/ui/Modal'
import { useStore } from 'stores/useStore.ts'

import cls from './ModalReportProtocol.module.scss'

interface ModalProtocolProps {
  handleClose: VoidFunction
  handleAccept: VoidFunction
  closeText: string
  acceptText: string
  title: string
}

export const ModalReportProtocol = (props: ModalProtocolProps) => {
  const { handleClose, handleAccept, closeText, acceptText, title } = props
  const { reportsStore } = useStore()
  const { validateData } = reportsStore
  const commonList = useMemo(() => {
    const arr = []

    for (const item of validateData.warnings) {
      arr.push({ key: generateUUID(), value: item, type: 'warning' })
    }
    for (const item of validateData.errors) {
      arr.push({ key: generateUUID(), value: item, type: 'error' })
    }

    return arr
  }, [validateData, validateData.warnings, validateData.errors])

  return (
    <Modal
      open
      title={title}
      maxWidth='md'
      onClose={handleClose}
      className={cls.Modal}
      actions={
        <div className={cls.modalFooterRight}>
          <Button variant='outlined' onClick={handleClose}>
            {closeText}
          </Button>
          <Button onClick={handleAccept}>{acceptText}</Button>
        </div>
      }
    >
      <div className={cls.wrapper}>
        <ul className={cls.list}>
          {commonList.map((item) => (
            <li key={item.key} className={cls.listItem}>
              {item.type === 'warning' && (
                <div className={`${cls.icon} ${cls.iconWarning}`}>
                  <PriorityHighIcon color='warning' width={20} height={20} />
                </div>
              )}
              {item.type === 'error' && (
                <div className={`${cls.icon} ${cls.iconError}`}>
                  <CloseRoundedIcon className={cls.ErrorIcon} color='error' width={20} height={20} />
                </div>
              )}
              {item.value}
            </li>
          ))}
        </ul>
      </div>
    </Modal>
  )
}

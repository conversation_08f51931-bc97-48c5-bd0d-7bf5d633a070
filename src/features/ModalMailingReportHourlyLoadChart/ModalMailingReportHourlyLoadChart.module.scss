.modalFooter {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
}

.wrapper {
  min-width: 600px;
  min-height: 550px;

  &Loading {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.sendButton {
  width: 90px;
  height: 26px;
}

.row {
  display: flex;
  margin: 0.5em 0;
  width: 100%;
}

.rowTable {
  width: 400px;
  max-height: 125px;
  margin: 0.5em 0 0.75em 200px;
}

.labelRow {
  flex-shrink: 0;
  width: 200px;
  font-weight: 600;
}

.valueBlock {
  width: 100%;

  &Short {
    width: 220px;
  }
}

.modalFooterRight {
  display: flex;
  align-items: center;
  gap: 1em;
}

.textFieldInput {
  width: 100%;
}

.btnProtocol {
  border-color: var(--red-color) !important;
  color: var(--text-color) !important;
}

.autocomplite {
  & > div > div {
    gap: 3px;
    padding-top: 3px !important;
    padding-bottom: 3px !important;
  }
  & > div > div > div {
    height: 1.75em !important;
    margin: 0 !important;
  }
  & > div > div > input {
    padding: 4px !important;
  }
}

.selectFontSize {
  [class*='MuiSelect'] {
    font-size: 12px !important;
  }

  [class*='MuiSelect-icon'] {
    top: unset;
  }

  & > div > div {
    padding: 0.25em 1em !important;
  }
}

export const validation = (values: { [key: string]: any }) => {
  const errors: any = {}

  const requiredFields = new Set(['fileNameTemplate', 'mailSubject', 'emails'])

  for (const [key, value] of Object.entries(values)) {
    if (requiredFields.has(key) && typeof value !== 'number' && !value?.length) {
      errors[key] = 'Поле должно быть заполнено'
    }
  }

  return [!Object.keys(errors).length, errors]
}

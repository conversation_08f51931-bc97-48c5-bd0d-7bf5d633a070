import { format, isEqual } from 'date-fns'
import { IStageList } from 'shared/lib/prepareReportDataTable/prepareReportAvrchmDataTable.ts'

export const getReportName = (
  fileNameTemplate: string,
  dateFrom: Date,
  dateTo: Date,
  stagesList: IStageList[],
  planingStage: string,
): string => {
  const isEqualDate = isEqual(dateFrom, dateTo)
  const date = isEqualDate
    ? format(dateFrom, 'dd.MM.yyyy')
    : `${format(dateFrom, 'dd.MM.yyyy')}-${format(dateTo, 'dd.MM.yyyy')}`
  const str = fileNameTemplate
    .replace(/<дата>/i, date)
    .replace(/<эп>/i, isEqualDate ? (stagesList.find((item) => item?.value === planingStage)?.label ?? '') : '')

  return str
}

const date = new Date()
date.setDate(date.getDate() + 1)

export interface IValue {
  name?: string
  dateFrom: Date | null
  dateTo: Date | null
  unloadingFormat: {
    oldValue?: string
    newValue?: string
  }
  unloadingTemplate: {
    oldValue?: string
    newValue?: string
  }
  //:null | {
  //         oldValue?:string | number | boolean | (string | number | boolean | null)[] | null
  //         newValue?:string | number | boolean | (string | number | boolean | null)[] | null
  //     } | string
  fileNameTemplate?: string
  mailingAttachment?: string
  unloadingDirectories?: string[]
  emails?: string[]
  mailSubject?: string | null
  mailingReportDirectory?: string | null
  plants?: { name: string }[]
  messageBody?: string
}

export const INITIAL_VALUES = {
  name: '',
  dateFrom: date,
  dateTo: date,
  unloadingFormat: {
    oldValue: '',
    newValue: '',
  },
  unloadingTemplate: {
    oldValue: '',
    newValue: '',
  },
  fileNameTemplate: '',
  unloadingDirectories: [],
  mailingAttachment: 'EXISTING_REPORT',
  mailSubject: null,
  mailingReportDirectory: null,
  emails: [],
}

import { addDays, format, isAfter, isBefore, parse } from 'date-fns'
import { getReportName } from 'features/ModalMailingReportHourlyLoadChart/lib/getReportName.ts'
import { ModalReportProtocol } from 'features/ModalReportProtocol'
import { useEffect, useMemo, useRef, useState } from 'react'
import api from 'shared/api/index.ts'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { IStageList } from 'shared/lib/prepareReportDataTable/prepareReportAvrchmDataTable.ts'
import { IRow, prepareReportHourlyDataTable } from 'shared/lib/prepareReportDataTable/prepareReportHourlyDataTable.ts'
import { AutocompleteEmails, AutocompleteEmailsProps } from 'shared/ui/AutocompleteEmails'
import { Button } from 'shared/ui/Button/index.ts'
import { DateRangePicker } from 'shared/ui/DateRangePicker/index.ts'
import { DataPickerValue, DatePickerContext } from 'shared/ui/DateRangePicker/ui/DateRangePicker.tsx'
import { Loader } from 'shared/ui/Loader'
import { LoadingButton } from 'shared/ui/LoadingButton/index.ts'
import { Modal } from 'shared/ui/Modal/index.ts'
import { ModalWarning } from 'shared/ui/ModalWarning/ModalWarning.tsx'
import { Select } from 'shared/ui/Select/index.ts'
import { TextField } from 'shared/ui/TextField/index.ts'
import { TextFieldWithPrompt } from 'shared/ui/TextFieldWithPrompt/TextFieldWithPrompt.tsx'
import { useStore } from 'stores/useStore.ts'
import { Table, TableProps } from 'widgets/Table'

import { INITIAL_VALUES, IValue } from './config/const.ts'
import { validation } from './lib/validation.ts'
import cls from './ModalMailingReportHourlyLoadChart.module.scss'

interface Props {
  reportType?: string
  id?: number
  onClose: VoidFunction
}

export const ModalMailingReportHourlyLoadChart = (props: Props) => {
  const { reportType, id, onClose } = props
  const { reportsStore, notificationStore } = useStore()
  const {
    unloadingFormatList,
    unloadingTemplateList,
    namePlaceholders,
    typesMailingAttachment,
    fetchUnloadingFormatList,
    fetchUnloadingTemplateList,
    fetchNamePlaceholders,
    fetchMailingAttachment,
    validateMailing,
    validateData,
  } = reportsStore

  const [values, setValues] = useState<IValue>(INITIAL_VALUES)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [isLoadingValues, setIsLoadingValues] = useState(false)
  const [isVisibleModalProtocol, setIsVisibleModalProtocol] = useState(false)

  const fetchReport = async (id: number) => {
    try {
      const res = await api.reportsManager.getReport(id)

      setValues((prev) => {
        return {
          ...prev,
          name: res.name,
          unloadingFormat: {
            oldValue: res?.settings?.unloadingFormat,
            newValue: res?.settings?.unloadingFormat,
          },
          unloadingTemplate: {
            oldValue: res?.settings?.unloadingTemplate,
            newValue: res?.settings?.unloadingTemplate,
          },
          plants: res?.settings?.plants,
          fileNameTemplate: res?.settings?.fileNameTemplate,
          unloadingDirectories: res?.settings?.unloadingDirectories,
          mailSubject: res?.settings?.mailSubject,
          mailingReportDirectory: res?.settings?.mailingReportDirectory,
          mailingAttachment: res?.settings?.defaultMailingAttachment,
          emails: res?.settings?.emails,
          messageBody: res?.settings?.messageBody,
        }
      })
    } catch (error) {
      console.log(error)
    }
  }

  const fetchStages = async (dateFrom: Date, dateTo: Date) => {
    let date = dateFrom
    const calcDates = [format(date, 'yyyy-MM-dd')]
    while (isBefore(date, dateTo)) {
      date = addDays(date, 1)
      calcDates.push(format(date, 'yyyy-MM-dd'))
    }

    try {
      const res = await api.calculationsManager.getListStages({
        calcDates,
      })
      setRows(prepareReportHourlyDataTable(res.dateStages))
    } catch (error) {
      console.log(error)
    }
  }

  const fetchUnloadingDates = async (id: number) => {
    try {
      const res = await api.reportsManager.getUnloadingDates(id)

      const dates = Object.keys(res.dateStages)
      if (dates.length) {
        let minDate = new Date(Object.keys(res.dateStages)[0])
        let maxDate = new Date(Object.keys(res.dateStages)[0])

        for (const date of dates) {
          if (isBefore(new Date(date), minDate)) minDate = new Date(date)
          else if (isAfter(new Date(date), maxDate)) maxDate = new Date(date)
        }

        setValues((prev) => ({
          ...prev,
          dateFrom: minDate,
          dateTo: maxDate,
        }))
      }

      const [firstDataItem] = Object.values(res.dateStages)
      if (firstDataItem) {
        const res = firstDataItem.stages.map((item) => ({
          value: item.code,
          label: item.title,
        }))
        setStagesList(res as IStageList[])
      }

      setRows(prepareReportHourlyDataTable(res.dateStages))
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    const getData = async () => {
      try {
        setIsLoadingValues(true)

        await Promise.all([
          fetchMailingAttachment(),
          fetchUnloadingFormatList(),
          fetchUnloadingTemplateList(),
          fetchNamePlaceholders(),
        ])
        if (id) {
          await Promise.all([fetchReport(id), fetchUnloadingDates(id)])
        }
      } catch (error) {
        console.log(error)
      } finally {
        setIsLoadingValues(false)
      }
    }

    getData()

    return () => {
      reportsStore.resetValidateData()
    }
  }, [])

  const [validationInProgress, setValidationInProgress] = useState(false)

  const handleValidateMailing = async () => {
    const [isValid, errors] = validation(values)
    setErrors(errors)
    if (!isValid) return

    const calculationRequests = rows.map((item) => ({
      targetDate: format(parse(item.date, 'dd.MM.yyyy', new Date()), 'yyyy-MM-dd'),
      planingStage: item.planingStage,
    }))
    const params = {
      reportId: id,
      reportType,
      mailingParameters: {
        calculationRequests,
        unloadingFormat: values?.unloadingFormat?.newValue,
        unloadingTemplate: values?.unloadingTemplate?.newValue,
        fileNameTemplate: values.fileNameTemplate,
        emails: values.emails,
        mailSubject: values.mailSubject,
        mailingReportDirectory: values.mailingReportDirectory,
        mailingAttachment: values.mailingAttachment,
        messageBody: values?.messageBody,
      },
    }
    // При отправке ответа в формате CSV не нужно передавать шаблон выгрузки,
    // так как этот параметр не обрабатывается на сервере
    if (params.mailingParameters.unloadingFormat === 'CSV') {
      delete params.mailingParameters.unloadingTemplate
    }

    try {
      setValidationInProgress(true)
      const data = await validateMailing(params)

      if (data?.errors?.length || data?.warnings?.length) {
        setIsVisibleModalProtocol(true)
      } else if (!data?.errors?.length && !data?.warnings?.length) {
        handleMailing()
      }
    } catch (error) {
      console.log('error', error)
    } finally {
      setValidationInProgress(false)
    }
  }

  const mailingReportAbortControllerRef = useRef<AbortController | null>(null)

  const [isMailingReport, setIsMailingReport] = useState(false)
  const [reportMailingName, setReportMailingName] = useState('')

  const handleMailing = async () => {
    mailingReportAbortControllerRef.current?.abort()

    mailingReportAbortControllerRef.current = new AbortController()

    const calculationRequests = rows.map((item) => ({
      targetDate: format(parse(item.date, 'dd.MM.yyyy', new Date()), 'yyyy-MM-dd'),
      planingStage: item.planingStage,
    }))
    const params = {
      reportId: id,
      reportType,
      mailingParameters: {
        calculationRequests,
        unloadingFormat: values?.unloadingFormat?.newValue,
        unloadingTemplate: values?.unloadingTemplate?.newValue,
        fileNameTemplate: values.fileNameTemplate,
        emails: values.emails,
        mailSubject: values.mailSubject,
        mailingReportDirectory: values.mailingReportDirectory,
        mailingAttachment: values.mailingAttachment,
        messageBody: values?.messageBody,
      },
    }
    // При отправке ответа в формате CSV не нужно передавать шаблон выгрузки,
    // так как этот параметр не обрабатывается на сервере
    if (params.mailingParameters.unloadingFormat === 'CSV') {
      delete params.mailingParameters.unloadingTemplate
    }

    if (values.dateFrom && values.dateTo) {
      const reportName = getReportName(
        values.fileNameTemplate ?? '',
        values.dateFrom,
        values.dateTo,
        stagesList,
        rows[0].planingStage,
      )
      setReportMailingName(reportName)
    }

    try {
      setIsMailingReport(true)
      setIsVisibleModalProtocol(false)

      const unloadingResponse = await api.reportsManager.mailingReport(
        params,
        mailingReportAbortControllerRef.current.signal,
      )

      if (unloadingResponse.errors.length) {
        notificationStore.addNotification({
          title: 'Ошибка',
          description: unloadingResponse.result ?? '',
          type: 'warning',
        })

        reportsStore.validateData = unloadingResponse
        setIsVisibleModalProtocol(true)
      } else {
        notificationStore.addNotification({
          title: '',
          description: unloadingResponse.result ?? '',
          type: 'success',
        })

        onClose()
      }
    } catch (error) {
      console.log(error)
    } finally {
      setIsMailingReport(false)
      setIsVisibleModalWarning(false)
    }
  }

  const handleChangeValueEmails = (key: string, value: string[]) => {
    setValues((prev) => ({ ...prev, [key]: value }))
  }

  const handleChangeValue = (key: string, value: string) => {
    switch (key) {
      case 'unloadingTemplate':
        setValues((prev) => {
          return {
            ...prev,
            [key]: {
              ...prev[key],
              newValue: value,
            },
          }
        })
        break
      case 'unloadingFormat':
        setValues((prev) => {
          return {
            ...prev,
            [key]: {
              ...prev[key],
              newValue: value,
            },
            unloadingTemplate: {
              ...prev.unloadingTemplate,
              newValue: value === 'CSV' ? prev?.unloadingTemplate?.oldValue : prev?.unloadingTemplate?.newValue,
            },
          }
        })
        break
      case 'mailingAttachment':
        setValues((prev) => {
          return {
            ...prev,
            [key]: value,
            unloadingFormat: {
              ...prev.unloadingFormat,
              newValue: value === 'EXISTING_REPORT' ? prev?.unloadingFormat?.oldValue : prev?.unloadingFormat?.newValue,
            },
            unloadingTemplate: {
              ...prev.unloadingTemplate,
              newValue:
                value === 'EXISTING_REPORT' ? prev?.unloadingTemplate?.oldValue : prev?.unloadingTemplate?.newValue,
            },
          }
        })
        break
      default:
        setValues((prev) => ({ ...prev, [key]: value }))
        break
    }

    if (errors[key] !== undefined)
      setErrors((prev) => {
        const newObj = { ...prev }
        delete newObj[key]

        return newObj
      })

    reportsStore.resetValidateData()
  }

  const handleChangeDate = (value: DataPickerValue, context: DatePickerContext) => {
    if (context.validationError.some((item) => item)) return
    reportsStore.resetValidateData()
    const [from, to] = value
    setValues((prev) => ({
      ...prev,
      dateFrom: from,
      dateTo: to,
    }))
    from && to && fetchStages(from, to)
  }
  const refTableBlock = useRef<HTMLDivElement>(null)

  const [rows, setRows] = useState<IRow[]>([])
  const [stagesList, setStagesList] = useState<IStageList[]>([])

  const columns = [
    {
      name: 'date',
      title: 'Дата',
      width: 200,
    },
    {
      name: 'planingStage',
      title: 'Этап планирования',
      width: 200,
      editingEnabled: true,
      editType: 'select',
      selectDataFromRow: 'stages',
      render: (value: IStageList, row: IRow) => {
        const currentItem = row.stages.find((el) => el.value === value)

        return <>{currentItem?.label ?? ''}</>
      },
    },
  ]

  const heightTable = useMemo(() => {
    return refTableBlock?.current?.clientHeight ?? 125
  }, [refTableBlock?.current?.clientHeight])

  //table
  //

  const [isVisibleModalWarning, setIsVisibleModalWarning] = useState(false)

  const handleClose = () => {
    if (isMailingReport) {
      setIsVisibleModalWarning(true)
    } else {
      onClose()
    }
  }

  const handleResetMailing = () => {
    mailingReportAbortControllerRef.current?.abort()

    setIsVisibleModalWarning(false)
    onClose()
  }

  const listOfDirectories = useMemo(() => {
    const arr = values.unloadingDirectories

    return Array.isArray(arr) ? arr.map((item) => ({ value: item, label: item })) : []
  }, [values.unloadingDirectories])

  const handleEmailsError: AutocompleteEmailsProps['onError'] = (error) => {
    setErrors((prev) => {
      const newErrors = { ...prev }
      if (!error) {
        delete newErrors.emails

        return newErrors
      }

      return {
        ...newErrors,
        emails: error,
      }
    })
  }

  return (
    <Modal
      open
      title='Рассылка отчёта почасового графика нагрузки'
      maxWidth='md'
      onClose={handleClose}
      actions={
        <div className={cls.modalFooter}>
          <div className={cls.modalFooterRight}>
            {validateData.errors.length > 0 && (
              <Button
                variant='outlined'
                onClick={() => setIsVisibleModalProtocol(true)}
                disabled={isLoadingValues || isMailingReport}
                className={cls.btnProtocol}
              >
                Протокол рассылки
              </Button>
            )}
            <Button variant='outlined' onClick={handleClose} disabled={isMailingReport}>
              Отменить
            </Button>

            <LoadingButton
              onClick={handleValidateMailing}
              variant='contained'
              className={cls.sendButton}
              disabled={validateData.errors.length > 0 || isLoadingValues}
              loading={validationInProgress || isMailingReport}
            >
              Отправить
            </LoadingButton>
          </div>
        </div>
      }
    >
      <>
        <div
          className={classNames(
            cls.wrapper,
            {
              [cls.wrapperLoading]: isLoadingValues || isMailingReport,
            },
            [],
          )}
        >
          {isLoadingValues || isMailingReport ? (
            <Loader />
          ) : (
            <>
              <div className={cls.row}>
                <div className={cls.labelRow}>Название</div>
                <div className={cls.valueBlock}>
                  <TextField
                    style={{ width: '100%' }}
                    value={values?.name}
                    type='string'
                    disabled
                    onChange={(e) => handleChangeValue('name', e.target.value)}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Дата (план)</div>
                <div className={cls.valueBlockShort}>
                  <DateRangePicker
                    dateFrom={values.dateFrom}
                    dateTo={values.dateTo}
                    handleChangeDate={handleChangeDate}
                  />
                </div>
              </div>
              <div ref={refTableBlock} className={cls.rowTable}>
                <Table
                  columns={columns}
                  rows={rows}
                  setRows={setRows as TableProps['setRows']}
                  height={heightTable}
                  editMode
                  columnSearchDisabled={['date', 'planingStage']}
                />
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Вложение</div>
                <div className={cls.valueBlockShort}>
                  <Select
                    variant='outlined'
                    items={typesMailingAttachment}
                    value={values.mailingAttachment}
                    onChange={(value) => handleChangeValue('mailingAttachment', value)}
                    className={cls.selectFontSize}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Формат выгрузки</div>
                <div className={cls.valueBlockShort}>
                  <Select
                    variant='outlined'
                    items={unloadingFormatList}
                    value={values.unloadingFormat?.newValue ?? ''}
                    disabled={values.mailingAttachment === 'EXISTING_REPORT'}
                    onChange={(value) => handleChangeValue('unloadingFormat', value)}
                    className={cls.selectFontSize}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Шаблон выгрузки</div>
                <div className={cls.valueBlockShort}>
                  <Select
                    disabled={
                      values.unloadingFormat?.newValue === 'CSV' || values.mailingAttachment === 'EXISTING_REPORT'
                    }
                    variant='outlined'
                    items={unloadingTemplateList}
                    value={values.unloadingTemplate?.newValue}
                    onChange={(value) => handleChangeValue('unloadingTemplate', value)}
                    className={cls.selectFontSize}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Станции</div>
                <div className={cls.valueBlock}>
                  <TextField
                    value={values?.plants?.map((item) => item.name).join(', ')}
                    type='string'
                    multiline
                    className={cls.textFieldInput}
                    disabled
                    onChange={(e) => handleChangeValue('name', e.target.value)}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Шаблон названия файла</div>
                <div className={cls.valueBlock}>
                  <TextFieldWithPrompt
                    error={!!errors.fileNameTemplate?.length}
                    helperText={errors.fileNameTemplate ?? ''}
                    className={cls.textFieldPropmt}
                    value={values.fileNameTemplate}
                    items={namePlaceholders}
                    onChange={(value: string) => handleChangeValue('fileNameTemplate', value)}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Тема письма</div>
                <div className={cls.valueBlock}>
                  <TextFieldWithPrompt
                    error={!!errors.mailSubject?.length}
                    helperText={errors.mailSubject ?? ''}
                    className={cls.textFieldPropmt}
                    value={values.mailSubject ?? ''}
                    items={namePlaceholders}
                    onChange={(value: string) => handleChangeValue('mailSubject', value)}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Директория для рассылки</div>
                <div className={cls.valueBlock}>
                  <Select
                    variant='outlined'
                    items={listOfDirectories}
                    value={values.mailingReportDirectory ?? ''}
                    onChange={(value) => handleChangeValue('mailingReportDirectory', value)}
                    className={cls.selectFontSize}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Получатели рассылки</div>
                <div className={cls.valueBlock}>
                  <AutocompleteEmails
                    error={!!errors.emails?.length}
                    value={values.emails as string[]}
                    onValuesChange={(values) => handleChangeValueEmails('emails', values)}
                    onError={handleEmailsError}
                    className={cls.autocomplite}
                  />
                </div>
              </div>

              <div className={cls.row}>
                <div className={cls.labelRow}>От кого</div>
                <div className={cls.valueBlock}>
                  <TextField
                    multiline
                    rows={3}
                    maxLength={255}
                    value={values?.messageBody ?? ''}
                    type='string'
                    className={cls.textFieldInput}
                    onChange={(e) => handleChangeValue('messageBody', e.target.value)}
                  />
                </div>
              </div>
            </>
          )}
        </div>

        {isVisibleModalProtocol && (
          <ModalReportProtocol
            handleClose={() => setIsVisibleModalProtocol(false)}
            handleAccept={handleMailing}
            acceptText='Отправить'
            closeText='Отменить'
            title='Отправка отчёта'
          />
        )}

        {isVisibleModalWarning && (
          <ModalWarning
            description={`Рассылка отчёта "${reportMailingName}" будет прервана`}
            handleReset={handleResetMailing}
            handleClose={() => setIsVisibleModalWarning(false)}
          />
        )}
      </>
    </Modal>
  )
}

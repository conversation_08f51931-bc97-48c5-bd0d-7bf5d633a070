import { format } from 'date-fns'
import Handsontable from 'handsontable'
import { observer } from 'mobx-react'
import { useCallback, useEffect } from 'react'
import { classNames } from 'shared/lib/classNames'
import { Button } from 'shared/ui/Button'
import { DatePicker } from 'shared/ui/DatePicker'
import { Icon } from 'shared/ui/Icon'
import { Loader } from 'shared/ui/Loader'
import { IGAESCalculationTabStore } from 'stores/GAESCalculationsStore/GAESCalculationTabStore/GAESCalculationTabStore.types.ts'
import { SpreadsheetReact } from 'widgets/SpreadsheetReact'

import cls from './TelemetryTableSpreadsheet.module.scss'

export interface ITelemetryTable {
  plantId?: number
  editMode: boolean
  selectedDate: string
  setSelectedDate: (date: string) => void
  getTelemetry: (plantId: number, telemetryDate: string) => Promise<void>
  loadTelemetryByDate: (plantId: number, targetDate: string) => Promise<void>
  isLoadingTelemetry: boolean
  telemetrySpreadsheet: IGAESCalculationTabStore['telemetrySpreadsheet']
  useLegacyDatePicker?: boolean
}

export const TelemetryTableSpreadsheet = observer((props: ITelemetryTable) => {
  const {
    plantId,
    editMode,
    isLoadingTelemetry,
    selectedDate,
    setSelectedDate,
    getTelemetry,
    loadTelemetryByDate,
    telemetrySpreadsheet,
    useLegacyDatePicker = false,
  } = props

  useEffect(() => {
    if (plantId) {
      getTelemetry(plantId, selectedDate)
    }
  }, [plantId, selectedDate])

  /**
   * Стилизует заголовки столбцов
   * @param col - Индекс столбца.
   * @param TH - Элемент заголовка столбца.
   * @param level - Уровень вложенности.
   */
  const setColumnHeader: Handsontable.GridSettings['afterGetColHeader'] = (col, TH, level) => {
    const headerHeight = '15px'
    const headerText = col === -1 && level === 1 ? 'Час' : (TH.textContent ?? '')
    const fontWeight = '700'

    const headerStyle = `
      height: ${headerHeight};
      line-height: ${headerHeight};
      font-weight: ${fontWeight};
    `

    TH.innerHTML = `
        <div>
          <span style="${headerStyle}">
            ${headerText}
          </span>
        </div>
      `

    // Добавляет жирный стиль к первой строке заголовков столбцов
    if (level === 0) {
      TH.classList.add(cls.bold)
    }
  }

  const handleLoadTelemetryData = useCallback(async () => {
    if (plantId) {
      await loadTelemetryByDate(plantId, selectedDate)
    }
  }, [plantId, selectedDate])

  return (
    <div className={cls.container}>
      <div className={cls.headTable}>
        <div className={useLegacyDatePicker ? '' : cls.calendarWrapper}>
          <DatePicker
            isArrow
            className={cls.calendar}
            value={new Date(selectedDate)}
            setValue={(date) => setSelectedDate(format(date, 'yyyy-MM-dd'))}
            disabled={!editMode}
            useLegacyStyle={useLegacyDatePicker}
          />
        </div>
        <Button
          variant='text'
          className={classNames(
            cls.buttonUpdateTelemetryTable,
            {
              [cls.buttonUpdateTelemetryTableSpin]: isLoadingTelemetry,
            },
            [],
          )}
          disabled={!editMode || isLoadingTelemetry || !plantId}
          onClick={handleLoadTelemetryData}
        >
          <Icon width={14} name='loadTelemetry' />
        </Button>
      </div>
      {!plantId ? (
        <div className={cls.loader}>
          <Loader />
        </div>
      ) : (
        <SpreadsheetReact
          enableFormulasPlugin
          width={160}
          height={434}
          colWidths={60}
          rowHeaderWidth={40}
          maxRows={telemetrySpreadsheet.data.length > 0 ? telemetrySpreadsheet.data.length : undefined}
          columns={telemetrySpreadsheet.columns}
          data={telemetrySpreadsheet.data}
          nestedHeaders={telemetrySpreadsheet.nestedHeaders}
          cell={telemetrySpreadsheet.cell}
          rowHeaders={telemetrySpreadsheet.rowHeaders}
          afterGetColHeader={setColumnHeader}
        />
      )}
    </div>
  )
})

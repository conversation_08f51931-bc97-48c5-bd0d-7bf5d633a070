.headTable {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 5px;
  color: var(--text-color);
}

.loader {
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  background-color: var(--background-color-secondary);
}

.container {
  display: flex;
  flex-direction: column;
  gap: 11px;
  align-items: end;
}

@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

.buttonUpdateTelemetryTable {
  min-width: 20px !important;
  min-height: 20px !important;
  border-radius: 50% !important;
  padding: 0 !important;

  &Spin {
    pointer-events: auto !important;

    & svg {
      animation: spin-reverse 1.5s linear 0s infinite;
    }
  }
}

.calendar {
  width: 120px;
  height: 24px;
  font-variant-numeric: tabular-nums;

  &Wrapper {
    width: 220px;
  }
}

import cls from 'features/TelemetryTable/TelemetryTable.module.scss'
import { classNames } from 'shared/lib/classNames'
import { ITelemetryHour, ITelemetryRowItem } from 'shared/lib/prepareTelemetryTableData'

interface ITelemetryRowSummaryItem {
  [key: string]: ITelemetryHour | string
}

const renderValue = (hourData: ITelemetryHour) => {
  return (
    <span
      className={classNames('', {
        [cls.minHour]: hourData.minHour,
        [cls.maxHour]: hourData.maxHour,
      })}
    >
      {hourData.value === null || hourData.value === undefined ? '-' : +hourData.value}
    </span>
  )
}

const telemetryColumns = [
  {
    name: 'hour',
    title: 'Час',
    width: 56,
  },
  {
    name: 'vb',
    title: 'вб',
    width: 56,
    render: renderValue,
  },
  {
    name: 'nb',
    title: 'нб',
    width: 56,
    render: renderValue,
  },
  {
    name: 'napor',
    title: 'напор',
    width: 56,
    render: renderValue,
  },
  {
    name: 'pbr',
    title: 'ПБР',
    width: 56,
    render: renderValue,
  },
  {
    name: 'fact',
    title: 'Факт',
    width: 56,
    render: renderValue,
  },
]
const telemetryTotalSummaryItems = [
  { columnName: 'hour', type: 'sum' },
  { columnName: 'vb', type: 'avgExcludeNull' },
  { columnName: 'nb', type: 'avgExcludeNull' },
  { columnName: 'napor', type: 'avgExcludeNull' },
  { columnName: 'pbr', type: 'sum' },
  { columnName: 'fact', type: 'sum' },
]

const prepareRowBeforeExtractSummaryValue = (rows: ITelemetryRowItem[]): ITelemetryRowSummaryItem[] =>
  rows.map((rowItem) =>
    Object.entries(rowItem).reduce((acc, [key, value]) => {
      if (typeof value === 'object') {
        acc[key] = value.value
      } else {
        acc[key] = value
      }

      return acc
    }, {} as ITelemetryRowSummaryItem),
  )

export { prepareRowBeforeExtractSummaryValue, telemetryColumns, telemetryTotalSummaryItems }

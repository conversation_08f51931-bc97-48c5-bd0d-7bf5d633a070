import { format } from 'date-fns'
import {
  prepareRowBeforeExtractSummaryValue,
  telemetryColumns,
  telemetryTotalSummaryItems,
} from 'features/TelemetryTable/lib'
import { observer } from 'mobx-react'
import { useEffect } from 'react'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { ITelemetryRowItem } from 'shared/lib/prepareTelemetryTableData'
import { Button } from 'shared/ui/Button'
import { DatePicker } from 'shared/ui/DatePicker'
import { Icon } from 'shared/ui/Icon'
import { Loader } from 'shared/ui/Loader'
import { Table } from 'widgets/Table'

import cls from './TelemetryTable.module.scss'

export interface ITelemetryTable {
  plantId: number
  height: number
  classes: string
  editMode: boolean
  isRgu: boolean
  selectedDate: string
  setSelectedDate: (date: string) => void
  getTelemetry: (plantId: number, telemetryDate: string) => Promise<void>
  loadTelemetryByDate: (plantId: number, targetDate: string) => Promise<void>
  isLoadingTelemetry: boolean
  telemetryTableData: ITelemetryRowItem[]
  useLegacyDatePicker?: boolean
}

export const TelemetryTable = observer((props: ITelemetryTable) => {
  const {
    plantId,
    classes,
    height,
    isRgu,
    editMode,
    isLoadingTelemetry,
    selectedDate,
    setSelectedDate,
    getTelemetry,
    loadTelemetryByDate,
    telemetryTableData,
    useLegacyDatePicker = false,
  } = props

  useEffect(() => {
    getTelemetry(plantId, selectedDate)
  }, [plantId, selectedDate])

  return (
    <div className={classNames(cls.container, { [cls.IsNotRgu]: !isRgu }, [classes])}>
      <Table
        columns={telemetryColumns}
        rows={telemetryTableData}
        height={Number(height) + 29}
        ROW_HEIGHT={15}
        divisionBySum={1000}
        showSearchControls={false}
        showSortingControls={false}
        headerComponents={
          <div className={cls.headTable}>
            <div style={{ textWrap: 'nowrap' }}>Факт ОИК</div>
            <div className={useLegacyDatePicker ? '' : cls.calendarWrapper}>
              <DatePicker
                isArrow
                className={cls.calendar}
                value={new Date(selectedDate)}
                setValue={(date) => setSelectedDate(format(date, 'yyyy-MM-dd'))}
                disabled={!editMode}
                useLegacyStyle={useLegacyDatePicker}
              />
            </div>
            <Button
              variant='text'
              className={cls.ButtonUpdateTelemetryTable}
              disabled={!editMode}
              onClick={() => loadTelemetryByDate(plantId, selectedDate)}
              loading={isLoadingTelemetry}
            >
              <Icon width={14} name='loadTelemetry' />
            </Button>
          </div>
        }
        columnSearchDisabled={['hour', 'vb', 'nb', 'napor', 'pbr', 'fact']}
        totalSummaryItems={telemetryTotalSummaryItems}
        sumFixed={2}
        prepareRowBeforeExtractSummaryValue={prepareRowBeforeExtractSummaryValue}
      />
      {(isLoadingTelemetry || telemetryTableData.length === 0) && (
        <div className={cls.Loader}>
          <Loader />
        </div>
      )}
    </div>
  )
})

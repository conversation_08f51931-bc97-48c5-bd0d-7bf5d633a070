.headTable {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 5px;
  color: var(--text-color);
}

.Loader {
  position: absolute;
  inset: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: var(--background-color-secondary);
}

.IsNotRgu {
  margin-top: -18px !important;
}

.container {
  position: relative;
  overflow: hidden;

  div {
    font-size: 0.75rem;
  }

  td {
    line-height: 0.85rem !important;
    & > div {
      min-height: 16px !important;
      max-height: 16px !important;
      height: 16px !important;
    }
  }
}

.ButtonUpdateTelemetryTable {
  min-width: 20px !important;
  min-height: 20px !important;
  border-radius: 50% !important;
  padding: 0 !important;
}

.minHour {
  color: var(--green-color);
}

.maxHour {
  color: var(--red-color);
}

.calendar {
  width: 120px;
  height: 24px;
  font-variant-numeric: tabular-nums;

  &<PERSON><PERSON><PERSON> {
    width: 220px;
  }
}

.AuthorizationPage {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.Card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 450px;
  width: 450px;
  padding: 30px;
  margin: 0 auto;
  background-color: var(--background-color-secondary);
  box-shadow: 0 5px 18px 12px rgb(34 60 80 / 20%);
  border-radius: 20px;
}

.Title {
  margin: 5px 0;
}

.VersionProject {
  margin-top: 10px;
}

.Error {
  color: var(--red-color);
}

.ButtonLogin {
  width: 100%;
  //height: 38px !important;
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  background-color: var(--primary-color);
  //margin: 10px 0;

  &:hover {
    background-color: var(--primary-color-hover) !important;
  }

  &:disabled {
    background-color: var(--disabled-button-bg) !important;
  }
}

.DropDown {
  margin: 10px 0 !important;

  label {
    left: -10px !important;
  }
}

.TextFieldLogin {
  margin: 10px 0 !important;
  background-color: white !important;
}

.LogoContainer {
  color: var(--primary-color);
}

import { observer } from 'mobx-react'
import { type FC, useCallback, useEffect, useMemo, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { Button } from 'shared/ui/Button'
import { Modal } from 'shared/ui/Modal/Modal'
import { Transfer } from 'shared/ui/Transfer'
import { useStore } from 'stores/useStore'

import cls from './UserGroupsAssignment.module.scss'

interface IUserGroupsAssignment {
  role: string
  roleDescription: string
  groups: string[]
  availableGroups: string[]
  onClose: () => void
}

export const UserGroupsAssignment: FC<IUserGroupsAssignment> = observer((props) => {
  const { role, roleDescription, groups, availableGroups, onClose } = props
  const { accessControlStore } = useStore()
  const { ldapGroups, getGroups, saveGroupsForRole } = accessControlStore
  const [targetKeys, setTargetKeys] = useState<string[]>(groups)

  useHotkeys('ctrl+shift+s', () => hasChanges && handleSaveChanges())
  useHotkeys('ctrl+shift+x', () => hasChanges && handleResetChanges())

  useEffect(() => {
    getGroups()
  }, [])

  const dataSource = useMemo(() => {
    return ldapGroups.map((item: any) => ({
      key: item,
      title: item,
      disabled: availableGroups.includes(item),
    }))
  }, [ldapGroups])

  const onChange = useCallback((nextTargetKeys: string[]) => {
    setTargetKeys(nextTargetKeys)
  }, [])

  const handleResetChanges = useCallback(() => {
    setTargetKeys(groups)
  }, [groups])

  const handleSaveChanges = useCallback(() => {
    const data = {
      role,
      groups: targetKeys,
    }
    saveGroupsForRole(data).then(() => {
      onClose()
    })
  }, [role, targetKeys])

  const hasChanges = useMemo(() => {
    return groups.toString() !== targetKeys.toString()
  }, [groups, targetKeys])

  return (
    <Modal
      title='Назначение групп пользователей'
      onClose={onClose}
      open
      maxWidth='xl'
      actions={
        <div className={cls.DrawerFooter}>
          <Button variant='outlined' onClick={handleResetChanges} disabled={!hasChanges}>
            Сбросить
          </Button>
          <Button variant='contained' onClick={handleSaveChanges} disabled={!hasChanges}>
            Сохранить
          </Button>
        </div>
      }
    >
      <div className={cls.TransferContainer}>
        <Transfer
          choicesTitle='Все группы'
          chosenTitle={`Группы ${roleDescription}`}
          dataSource={dataSource}
          targetKeys={targetKeys}
          onChange={onChange}
        />
      </div>
    </Modal>
  )
})

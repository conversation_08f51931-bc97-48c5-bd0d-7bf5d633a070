import Checkbox from '@mui/material/Checkbox'
import { observer } from 'mobx-react-lite'
import { type FC, useEffect, useMemo } from 'react'
import { Modal } from 'shared/ui/Modal/Modal'
import { useStore } from 'stores/useStore'
import { Table } from 'widgets/Table'

interface IPermissionsTable {
  role: string
  onClose: () => void
}

export const PermissionsTable: FC<IPermissionsTable> = observer((props) => {
  const { role, onClose } = props
  const { accessControlStore } = useStore()
  const { permissions, getPermissionsByRole } = accessControlStore

  useEffect(() => {
    getPermissionsByRole(role)
  }, [])

  const rows = useMemo(() => {
    return (
      permissions.map((item) => ({
        tabId: item.name,
        name: item.name,
        readAllowed: <Checkbox checked={item.readAllowed} />,
        modifyAllowed: <Checkbox checked={item.modifyAllowed} />,
      })) ?? []
    )
  }, [permissions])

  const columns = [
    {
      name: 'name',
      title: 'Раздел',
      width: 200,
    },
    {
      name: 'readAllowed',
      title: 'Просмотр',
      width: 200,
    },
    {
      name: 'modifyAllowed',
      title: 'Редактирование/Расчет',
      width: 200,
    },
  ]

  return (
    <Modal title='Список привилегий' onClose={onClose}>
      <Table rows={rows} columns={columns} />
    </Modal>
  )
})

import { observer } from 'mobx-react-lite'
import { type FC, useEffect, useMemo } from 'react'
import { Modal } from 'shared/ui/Modal/Modal'
import { useStore } from 'stores/useStore'
import { Table } from 'widgets/Table'

import cls from './UsersTable.module.scss'

interface IUsersTable {
  role: string
  onClose: () => void
}

export const UsersTable: FC<IUsersTable> = observer((props) => {
  const { role, onClose } = props
  const { accessControlStore } = useStore()
  const { users, getUsersByRole } = accessControlStore

  useEffect(() => {
    getUsersByRole(role)
  }, [])

  const rows = useMemo(() => {
    return (
      users.map((item) => ({
        ...item,
        tabId: item.user,
      })) ?? []
    )
  }, [users])

  const columns = [
    {
      name: 'user',
      title: 'ФИО',
      width: 320,
    },
    {
      name: 'group',
      title: 'Группа AD',
      width: 200,
    },
  ]

  return (
    <Modal title='Список пользователей' open onClose={onClose}>
      <Table rows={rows} columns={columns} height={600} className={cls.usersTable} />
    </Modal>
  )
})

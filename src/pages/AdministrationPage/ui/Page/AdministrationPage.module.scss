.AdministrationPage {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.Header {
  height: var(--header-page);
  background-color: var(--background-color-secondary);
  border-radius: 8px;
  box-shadow: var(--shadow-page);
  padding: 0 16px;
}

.Body {
  border-radius: 8px;
  background-color: var(--background-color-secondary);
  box-shadow: var(--shadow-page);
  margin-top: 0.2rem;
  height: 100%;
  padding: 0.2rem;
  overflow: hidden;
}

.GroupLi {
  & + .GroupLi {
    border-top: 1px solid rgba(224, 224, 224, 1);
  }
}

.ActionRow {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5em;
}

.ActionHeader {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ActionButton {
  padding: 0 !important;
  margin: 0 !important;
  justify-content: left !important;
}

import { AccountCircleOutlined, GroupAddOutlined } from '@mui/icons-material'
import { IconButton, Tooltip } from '@mui/material'
import { TIME_LOADER } from 'entities/constants.ts'
import { observer } from 'mobx-react-lite'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'
import { SubtitleWithActions } from 'shared/ui/SubtitleWithActions'
import { IRolesResponse } from 'stores/AccessControlStore/AccessControlStore.type'
import { useStore } from 'stores/useStore'
import { Table } from 'widgets/Table'
import { Tabs } from 'widgets/Tabs'
import { ItemsProps } from 'widgets/Tabs/Tabs'

import { UserGroupsAssignment } from '../UserGroupsAssignment/UserGroupsAssignment'
import { UsersTable } from '../UsersTable/UsersTable'
import cls from './AdministrationPage.module.scss'

interface AdministrationPageProps {
  className?: string
}

interface IRolesTableDrawerState {
  permissions: boolean
  users: boolean
  assignment: boolean
}

const INITIAL_STATE = {
  permissions: false,
  users: false,
  assignment: false,
}

const AdministrationPage = observer((props: AdministrationPageProps) => {
  const { className } = props
  const { accessControlStore } = useStore()
  const { roles, getRoles } = accessControlStore

  const items = [
    {
      key: 'access_control',
      label: `Управление доступом`,
      icon: 'book',
    },
  ]

  const [tab, setTab] = useState('access_control')
  const onChangeTabs = (key: string) => {
    setTab(key)
  }

  const [activeRole, setActiveRole] = useState('')
  const [activeRoleDescription, setActiveRoleDescription] = useState('')
  const [activeGroups, setActiveGroups] = useState<string[]>([])
  const [openDrawer, setOpenDrawer] = useState<IRolesTableDrawerState>(INITIAL_STATE)

  const [loading, setLoading] = useState(false)

  useEffect(() => {
    setLoading(true)
    getRoles().then(() => {
      setTimeout(() => {
        setLoading(false)
      }, TIME_LOADER)
    })
  }, [])

  const rows = useMemo(() => {
    if (!roles) return []

    const rows = []
    for (const role of roles) {
      rows.push({
        tabId: role.userRole.role,
        role: role.userRole.description,
        groups: role.groups,
      })
    }

    return rows
  }, [roles])

  const availableGroups = useMemo(() => {
    const filterGroups: string[] = []
    roles
      .filter((item) => item.userRole.role !== activeRole)
      .forEach((item) => {
        item.groups.forEach((group) => filterGroups.push(group))
      })

    return filterGroups
  }, [activeRole, roles])

  const columns = [
    {
      name: 'role',
      title: 'Роль',
      width: 600,
    },
    {
      name: 'groups',
      title: 'Группа AD',
      width: 600,
      isBlockedSorting: true,
      render: (groups: string[]) => {
        return (
          <ul>
            {groups.map((item: string) => (
              <li key={item} className={cls.GroupLi}>
                {item}
              </li>
            ))}
          </ul>
        )
      },
    },
    {
      name: 'action',
      title: 'Действия',
      width: 600,
      isBlockedSorting: true,
      headRender: () => {
        return <div className={cls.ActionHeader}>Действия</div>
      },
      render: (_: unknown, row: { tabId: string }) => {
        return (
          <div className={cls.ActionRow}>
            <Tooltip title='Назначение групп пользователей'>
              <IconButton
                className={cls.ActionButton}
                onClick={() => {
                  const find = roles.find((item) => item.userRole.role === row.tabId)
                  find && handleOpenDrawer(find, 'assignment')
                }}
              >
                <GroupAddOutlined />
              </IconButton>
            </Tooltip>
            {/* <Tooltip title="Список привилегий">
              <IconButton
                onClick={() => {
                  handleOpenDrawer(
                    roles.find((item: any) => item.userRole.role === row.tabId),
                    "permissions"
                  );
                }}
              >
                <ControlOutlined />
              </IconButton>
            </Tooltip> */}
            <Tooltip title='Список пользователей'>
              <IconButton
                className={cls.ActionButton}
                onClick={() => {
                  const find = roles.find((item) => item.userRole.role === row.tabId)
                  find && handleOpenDrawer(find, 'users')
                }}
              >
                <AccountCircleOutlined />
              </IconButton>
            </Tooltip>
          </div>
        )
      },
    },
  ]

  const handleOpenDrawer = useCallback((item: IRolesResponse, key: keyof IRolesTableDrawerState) => {
    const { userRole, groups } = item
    const { role, description } = userRole
    if (key === 'assignment') {
      setActiveGroups(groups)
    }
    setOpenDrawer((prev) => ({
      ...prev,
      [key]: true,
    }))
    setActiveRole(role)
    setActiveRoleDescription(description)
  }, [])

  const handleCloseDrawer = useCallback((key: keyof IRolesTableDrawerState) => {
    if (key === 'assignment') {
      setActiveGroups([])
    }
    setOpenDrawer((prev) => ({
      ...prev,
      [key]: false,
    }))
    setActiveRole('')
    setActiveRoleDescription('')
  }, [])

  const bodyRef = useRef<HTMLDivElement>(null)
  const [height, setHeight] = useState<number | null>(null)

  useEffect(() => {
    if (bodyRef && bodyRef.current) {
      const curHeight = bodyRef.current.getBoundingClientRect().height
      setHeight(curHeight)
    }
  }, [bodyRef])

  return (
    <div className={classNames(cls.AdministrationPage, {}, className ? [className] : [])}>
      <div className={classNames(cls.Header, {}, [])}>
        <Tabs selectedValue={tab} items={items as ItemsProps[]} onChange={onChangeTabs} />
      </div>
      <div ref={bodyRef} className={classNames(cls.Body, {}, [])}>
        <SubtitleWithActions title='Доступы' />

        <Table
          rows={rows}
          loading={loading}
          columns={columns}
          height={height}
          className={cls.table}
          columnSearchDisabled={['action']}
        />

        {/* {openDrawer.permissions && (
          <PermissionsTable
            role={activeRole}
            onClose={() => handleCloseDrawer("permissions")}
          />
        )} */}
        {openDrawer.users && (
          <UsersTable
            role={activeRole}
            onClose={() => {
              handleCloseDrawer('users')
            }}
          />
        )}
        {openDrawer.assignment && (
          <UserGroupsAssignment
            groups={activeGroups}
            role={activeRole}
            roleDescription={activeRoleDescription}
            availableGroups={availableGroups}
            onClose={() => {
              handleCloseDrawer('assignment')
            }}
          />
        )}
      </div>
    </div>
  )
})

export default AdministrationPage

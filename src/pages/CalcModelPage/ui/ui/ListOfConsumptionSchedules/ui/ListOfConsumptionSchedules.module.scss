.ListOfConsumptionSchedules {
  //width: 100%;
  //height: 100%;
  //display: flex;
  //background-color: var(--background-color-primary);
  //overflow: auto;
  //flex-direction: column;
  padding: 0.2rem;
  height: 100%;
}

.Header {
  border-radius: 8px;
  background: var(--gray-background);
  height: 40px;
  min-height: 40px;
  max-height: 40px;
  width: 100%;
  display: flex;
  //justify-content: space-around;
  align-items: center;
  padding: 10px 20px;
}

.HeaderTitle {
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  color: var(--text-color);
}

.AddButton {
  margin-left: auto;
  margin-right: 10px;
}

.Body {
  width: 100%;
  height: 92%;
  //border: solid 1px red;
  display: flex;
  //justify-content: space-around;
  //gap: 3em;
}

.Trash {
  padding: 0 !important;
  color: var(--red-color) !important;
  width: 18px;
  height: 18px;
  min-width: 18px;
  min-height: 18px;
  margin: 0 10px;
}

.Settings {
  padding: 0 !important;
  width: 18px;
  height: 18px;
  min-width: 18px;
  min-height: 18px;
  margin: 0 10px;
}

.iconBtnSetting {
  padding: 0 !important;
  margin: 0 !important;
  justify-content: left !important;
}

.iconBtnSetting:hover {
  color: var(--primary-color);
}

.iconBtnTrash {
  padding: 0 !important;
}

.iconBtnTrash:hover {
  color: var(--red-color);
}

.formulaCell {
}

.AddIcon {
  stroke: var(--primary-color);
  stroke-width: 0.5;
}

.ActionHeader {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.IconCell {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

import { Icon<PERSON><PERSON>on, Tooltip } from '@mui/material'
import { IAddConsumptionSchedulesInput } from 'entities/api/calcModelPage.entities.ts'
import { TIME_LOADER } from 'entities/constants.ts'
import {
  IObjectAddAndEdit,
  IRowListOfConsumptionSchedules,
  ListOfConsumptionSchedulesProps,
} from 'entities/pages/calcModelPage.entities.ts'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { observer } from 'mobx-react'
import { useEffect, useRef, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import api from 'shared/api/index'
import { classNames } from 'shared/lib/classNames/classNames'
import { Button } from 'shared/ui/Button'
import { CheckEditComponent } from 'shared/ui/CheckEditComponent'
import { Icon } from 'shared/ui/Icon'
import { SubtitleWithActions } from 'shared/ui/SubtitleWithActions'
import { IConsumptionSchedules } from 'stores/CalcModelStore'
import { useStore } from 'stores/useStore'
import { Table } from 'widgets/Table'

import { AddAndEditConsumptionSchedules } from '../../AddAndEditConsumptionSchedules'
import cls from './ListOfConsumptionSchedules.module.scss'

export const ListOfConsumptionSchedules = observer((props: ListOfConsumptionSchedulesProps) => {
  const { calcModelStore } = useStore()
  const { intConsumptionSchedules, consumptionSchedules, addConsumptionSchedules, saveConsumptionSchedules } =
    calcModelStore
  const { className, userDetail } = props

  useEffect(() => {
    intConsumptionSchedules()
  }, [])

  const [loading, setLoading] = useState(true)
  const [filteredConsumptionSchedules, setFilteredConsumptionSchedules] = useState<IConsumptionSchedules[]>([])
  const [scheduleIds, setScheduleIds] = useState<number[]>([])

  useEffect(() => {
    setLoading(true)
    setFilteredConsumptionSchedules(consumptionSchedules)
    setTimeout(() => {
      setLoading(false)
    }, TIME_LOADER)
  }, [consumptionSchedules])
  const [objectAddAndEdit, setObjectAddAndEdit] = useState<IObjectAddAndEdit | null>(null)
  const accessRole = [ROLES.TECH_ADMIN_CM]
  const { roles } = userDetail
  const editMode = roles
    ? roles
        .map((el) => el.role)
        .some((el: string) => {
          return accessRole.some((item) => item === el)
        })
    : false

  const handleDeleteObject = (id: number) => {
    setScheduleIds((prev) => [...prev, id])
    setFilteredConsumptionSchedules((prev) => prev.filter((item) => item.id !== id))
  }

  const handleReset = () => {
    setFilteredConsumptionSchedules(consumptionSchedules)
    setScheduleIds([])
  }

  const handleSave = async () => {
    await api.calcModelManager.deleteConsumptionScheduleMultiple({
      scheduleIds,
    })
    setScheduleIds([])
    await intConsumptionSchedules()
  }

  const columns = [
    {
      name: 'energyDistrictName',
      title: 'Название Территории/Энергорайона',
      width: 400,
      customSorting: (a: string, b: string) => {
        return a.toUpperCase() < b.toUpperCase() ? -1 : 1
      },
    },
    { name: 'energyDistrictIspId', title: 'ID ИСП', width: 300 },
    { name: 'notation', title: 'Примечание', width: 300 },
    {
      name: 'formula',
      title: 'Формула',
      width: 150,
      headRender: () => {
        return <div className={cls.ActionHeader}>Формула</div>
      },
      render: (_: never, row: IRowListOfConsumptionSchedules) => {
        return editMode ? (
          <div className={cls.IconCell}>
            <IconButton
              onClick={() => {
                setObjectAddAndEdit({
                  type: 'edit',
                  energyDistrictIspId: row.energyDistrictIspId,
                  id: row.id,
                  notation: row.notation,
                })
              }}
              className={cls.iconBtnSetting}
            >
              <Icon name='settings' width={15} />
            </IconButton>
          </div>
        ) : (
          <></>
        )
      },
    },
    {
      name: 'actions',
      title: '',
      width: 65,
      isHeadLabelCenter: true,
      headRender: () => {
        if (editMode) {
          return (
            <div className={cls.ActionHeader}>
              <Tooltip title='Создать территорию'>
                <IconButton
                  className={cls.AddIcon}
                  onClick={() => {
                    setObjectAddAndEdit({ type: 'add' })
                  }}
                  sx={{
                    color: 'var(--primary-color)',
                  }}
                >
                  <Icon name='plus' width={13} />
                </IconButton>
              </Tooltip>
            </div>
          )
        } else {
          return <></>
        }
      },
      render: (_: never, row: IRowListOfConsumptionSchedules) => {
        return editMode && row.canDelete ? (
          <div className={cls.IconCell}>
            <Tooltip title='Удалить территорию'>
              <IconButton
                onClick={() => {
                  handleDeleteObject(row.id)
                  // setObjectDelete({ value: row.id, label: row.energyDistrictName })
                }}
                className={cls.iconBtnTrash}
              >
                <Icon name='trash' width={14} height={14} />
              </IconButton>
            </Tooltip>
          </div>
        ) : (
          <></>
        )
      },
    },
  ]

  const bodyRef = useRef<HTMLDivElement>(null)
  const [height, setHeight] = useState<number | null>(null)

  useEffect(() => {
    if (bodyRef && bodyRef.current) {
      const curHeight = bodyRef.current.getBoundingClientRect().height
      setHeight(curHeight)
    }
  }, [bodyRef])

  useHotkeys('ctrl+shift+s', () => scheduleIds.length && handleSave())
  useHotkeys('ctrl+shift+x', () => scheduleIds.length && handleReset())

  const isEdit = filteredConsumptionSchedules.some((el) => el?.tempEdits && el?.tempEdits?.length > 0)

  return (
    <CheckEditComponent isEdit={scheduleIds.length > 0 || isEdit}>
      <div className={classNames(cls.ListOfConsumptionSchedules, {}, className ? [className] : [])}>
        <SubtitleWithActions
          title='Территории'
          actions={
            editMode
              ? [
                  <Button disabled={!scheduleIds.length} variant='outlined' onClick={handleReset}>
                    Сбросить
                  </Button>,
                  <Button disabled={!scheduleIds.length} onClick={handleSave}>
                    Сохранить
                  </Button>,
                ]
              : []
          }
          isActionsVisible
        />
        <div ref={bodyRef} className={classNames(cls.Body, {}, [])}>
          <Table
            columns={columns}
            loading={loading}
            rows={filteredConsumptionSchedules}
            height={height}
            columnSearchDisabled={['actions', 'formula']}
          />
        </div>
        {objectAddAndEdit && (
          <AddAndEditConsumptionSchedules
            object={objectAddAndEdit}
            onClose={() => {
              setObjectAddAndEdit(null)
            }}
            onConfirm={async (res) => {
              if (objectAddAndEdit.type === 'add') {
                await addConsumptionSchedules(res as IAddConsumptionSchedulesInput).then(() => {
                  intConsumptionSchedules()
                })
              } else {
                await saveConsumptionSchedules(res as IAddConsumptionSchedulesInput).then(() => {
                  intConsumptionSchedules()
                })
              }
              setObjectAddAndEdit(null)
            }}
          />
        )}
      </div>
    </CheckEditComponent>
  )
})

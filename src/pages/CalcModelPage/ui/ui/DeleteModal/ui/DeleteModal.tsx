import { Tooltip } from '@mui/material'
import { classNames } from 'shared/lib/classNames/classNames'
import { Button } from 'shared/ui/Button'
import { Modal } from 'shared/ui/Modal'

import cls from './DeleteModal.module.scss'

interface DeleteModalProps {
  className?: string
  onClose?: () => void
  onConfirm?: () => void
  item?: { label: string }
}

export const DeleteModal = (props: DeleteModalProps) => {
  const { className, onClose, item = { label: '' }, onConfirm } = props

  return (
    <Modal
      title={
        <div>
          Вы действительно хотите удалить{' '}
          <Tooltip placement='right' title={item.label}>
            <div className={classNames(cls.Label, {}, [])}>{item.label}</div>
          </Tooltip>
          ?
        </div>
      }
      onClose={onClose}
      className={classNames(cls.DeleteModal, {}, className ? [className] : [])}
      actions={
        <div className={classNames(cls.ButtonsContainer, {}, [])}>
          <div className={classNames(cls.Buttons, {}, [])}>
            <Button className={classNames(cls.MuiButtonOutlined, {}, [])} variant='outlined' onClick={onClose}>
              Отменить
            </Button>
            <Button className={classNames(cls.MuiButtonContained, {}, [])} onClick={onConfirm}>
              Удалить
            </Button>
          </div>
        </div>
      }
    />
  )
}

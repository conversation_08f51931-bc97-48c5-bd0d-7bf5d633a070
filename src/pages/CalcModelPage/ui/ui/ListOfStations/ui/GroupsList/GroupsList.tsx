import AddIcon from '@mui/icons-material/Add'
import RemoveIcon from '@mui/icons-material/Remove'
import { IconButton, Tooltip } from '@mui/material'
import { Dispatch, ReactNode, SetStateAction, useState } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'
import { Icon } from 'shared/ui/Icon'

import cls from './GroupsList.module.scss'

export interface IItem {
  value: string
  name: string
  label?: string
  id: string
  ids: string[]
  childs: IItem[]
}

interface IGreatGroup {
  type: string
  name: string
  initialSelect: string[]
  initialSelectArr: IItem[]
  valueUuid: string
}

interface GroupsListProps {
  className?: string
  items?: IItem[]
  setDeleteGroup?: Dispatch<IItem>
  disabled?: boolean
  isError?: boolean
  setCreateGroup?: Dispatch<IGreatGroup>
  errors?: Map<string, string>
}

interface IRenderRow {
  items: IItem[]
  expands: string[]
  setExpands: Dispatch<SetStateAction<string[]>> // Explicitly set the type of setExpands
  level: number
  setDeleteGroup: Dispatch<IItem>
  disabled: boolean
  setCreateGroup: Dispatch<IGreatGroup>
  errors: Map<string, string>
}

const renderRow = (props: IRenderRow): ReactNode[] => {
  const { items, expands, setExpands, level, setDeleteGroup, disabled, setCreateGroup, errors }: IRenderRow = props

  return items.map((item) => {
    const expanded = expands.some((el) => el === item.value)

    return (
      <>
        <div className={classNames(cls.Row, {}, [])}>
          <div className={cls.rowLeft}>
            {item?.childs?.length > 0 ? (
              <div
                style={{ margin: `0 ${level * 10}px` }}
                className={classNames(cls.IconContainer, { [cls.isIcon]: true }, [])}
                onClick={() =>
                  setExpands((prev) => {
                    const isFind = prev.some((el: string) => el === item.value)
                    if (isFind) {
                      return prev.filter((el: string) => el !== item.value)
                    } else {
                      return [...prev, item.value]
                    }
                  })
                }
              >
                {expanded ? <RemoveIcon width={16} /> : <AddIcon width={16} />}
              </div>
            ) : (
              <div style={{ margin: `0 ${level * 10}px` }} className={classNames(cls.IconContainer, {}, [])}></div>
            )}
            <>{item.name}</>
          </div>
          <div className={cls.rowRight}>
            {!item.id && !disabled && (
              <Tooltip title='Редактировать группу' className={classNames(cls.Trash, {}, [])}>
                <IconButton
                  onClick={() => {
                    setCreateGroup({
                      type: 'edit',
                      valueUuid: item.value,
                      name: item.name,
                      initialSelect: item.ids,
                      initialSelectArr: item.childs,
                    })
                    errors.delete('GENERATOR_JOINT_WORK_WATCH')
                  }}
                >
                  <Icon width={16} name='settings' />
                </IconButton>
              </Tooltip>
            )}
            {!item.id && !disabled && (
              <Tooltip title='Удалить группу' className={classNames(cls.Trash, {}, [])}>
                <IconButton
                  onClick={() => {
                    setDeleteGroup({
                      ...item,
                      label: item.name,
                    })
                  }}
                >
                  <Icon width={12} name='trash' />
                </IconButton>
              </Tooltip>
            )}
          </div>
        </div>
        {expanded &&
          renderRow({
            items: item.childs,
            expands,
            setExpands,
            level: level + 1,
            setDeleteGroup,
            disabled,
            setCreateGroup,
            errors,
          })}
      </>
    )
  })
}

export const GroupsList = (props: GroupsListProps) => {
  const {
    className,
    items = [],
    setDeleteGroup = () => {},
    disabled = false,
    isError,
    setCreateGroup = () => {},
    errors = new Map(),
  } = props
  const [expands, setExpands] = useState<string[]>([])

  return (
    <div className={classNames(cls.GroupsList, { [cls.GroupsListError]: isError }, className ? [className] : [])}>
      {renderRow({
        items,
        expands,
        setExpands,
        level: 0,
        setDeleteGroup,
        disabled,
        setCreateGroup,
        errors,
      })}
    </div>
  )
}

.GroupsList {
  width: 346px;
  height: 175px;
  border-radius: 8px;
  border: 1px solid rgb(0 0 0 / 26%);
  background: inherit;
  overflow: auto;
  padding: 5px;

  &Error {
    border: 1px solid var(--red-color);
  }
}

.Row {
  width: 100%;
  height: 20px;
  padding: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.IconContainer {
  width: 20px;
  height: 20px;
  min-width: 20px;
  min-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px !important;
  cursor: pointer;
}

.isIcon {
  border: solid 1px var(--text-gray);
  border-radius: 4px;
}

.Trash {
  margin-left: auto;
  margin-right: 4px;
  // padding: 0 !important;
  // min-width: 16px !important;
  // min-height: 16px !important;
  // width: 16px !important;
  // height: 16px !important;

  &:hover {
    color: var(--red-color) !important;
  }
}

.rowLeft,
.rowRight {
  display: flex;
  align-items: center;
}

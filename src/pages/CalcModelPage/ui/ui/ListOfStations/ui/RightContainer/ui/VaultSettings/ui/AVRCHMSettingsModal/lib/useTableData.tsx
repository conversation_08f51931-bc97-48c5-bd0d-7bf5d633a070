import { TableColumnResizingProps } from '@devexpress/dx-react-grid'
import { isAfter } from 'date-fns'
import {
  CalcModelAvrchmSettingsSource,
  CalcModelAvrchmSettingsSourceName,
  ICalcModelAvrchmSettingsColumn,
  IListOfStationsStore,
} from 'entities/store/calcModelStore.entities.ts'
import { useEffect, useState } from 'react'
import { useStore } from 'stores/useStore.ts'
import { TableHeaderActionButton, TableProps } from 'widgets/Table'

import { TableHeaderCell } from '../ui/TableHeaderCell'

const initialColumns = (
  setOpenModal: (opened: boolean) => void,
  addActionDisabled: boolean,
  hideAction: boolean,
): TableProps['columns'] => [
  {
    name: 'rowDescription',
    title: '',
    width: 160,
    isHeadLabelCenter: false,
  },
  {
    name: 'actions',
    title: '',
    width: 40,
    isHeadLabelCenter: true,
    headRender: () =>
      !hideAction && (
        <TableHeaderActionButton
          tooltip='Создать столбец'
          color='primary'
          iconName='plus'
          disabled={addActionDisabled}
          onClick={() => setOpenModal(true)}
        />
      ),
  },
]

type InitialRow = {
  rowDescription: string
  varName: ICalcModelAvrchmSettingsColumn | 'parameter'
  tabId: string
}

const initialRows: InitialRow[] = [
  {
    rowDescription: 'Наименование',
    varName: 'title',
    tabId: 'title',
  },
  {
    rowDescription: 'Источник данных',
    varName: 'source',
    tabId: 'source',
  },
  {
    rowDescription: 'Параметр/формула',
    varName: 'parameter',
    tabId: 'parameter',
  },
  {
    rowDescription: 'Коэффициент влияния',
    varName: 'influence',
    tabId: 'influence',
  },
  {
    rowDescription: 'Норматив',
    varName: 'minNorm',
    tabId: 'minNorm',
  },
]

const initialColumnSearchDisabled = ['rowDescription', 'actions']

export const useTableData = (editModeRole: boolean) => {
  const { calcModelStore } = useStore()
  const { listOfStationsStore } = calcModelStore
  const {
    avrchmSettings,
    moveAvrchmSettingsColumn,
    removeAvrchmSettingsColumn,
    editAvrchmSettingsColumn,
    avrchmSettingsTargetDate,
  } = listOfStationsStore
  const [openModal, setOpenModal] = useState(false)
  const [columns, setColumns] = useState(initialColumns(setOpenModal, true, !editModeRole))
  const [rows, setRows] = useState(initialRows)
  const [columnSearchDisabled, setColumnSearchDisabled] = useState(initialColumnSearchDisabled)

  const handleEditClick: IListOfStationsStore['editAvrchmSettingsColumn'] = (idx, action, newColumn) => {
    editAvrchmSettingsColumn(idx, action, newColumn)
    setOpenModal(true)
  }

  const updateColumnWidth: TableColumnResizingProps['onColumnWidthsChange'] = (nextColumnWidths) => {
    setColumns((prev) => {
      return prev.map((column, idx) => ({
        ...column,
        width: Number(nextColumnWidths[idx].width),
      }))
    })
  }

  useEffect(() => {
    let actionDisabled = false
    if (avrchmSettingsTargetDate) {
      const currentDate = new Date().setHours(0, 0, 0, 0)
      const avrchmSettingsDate = new Date(avrchmSettingsTargetDate).setHours(0, 0, 0, 0)
      actionDisabled = !isAfter(new Date(avrchmSettingsDate), new Date(currentDate))
    }

    const newColumns = initialColumns(setOpenModal, actionDisabled, !editModeRole)
    let newRows = initialRows
    const newColumnSearchDisabled = initialColumnSearchDisabled
    avrchmSettings?.columns.forEach((newColumn, idx, arr) => {
      newColumns.splice(idx + 1, 0, {
        name: String(idx + 1),
        title: '',
        width: 160,
        isHeadLabelCenter: false,
        headRender: () => (
          <TableHeaderCell
            title={String(idx + 1)}
            hideLeftArrow={idx === 0}
            hideRightArrow={idx === arr.length - 1}
            hideActions={!editModeRole}
            disableActions={actionDisabled}
            onLeftClick={() => moveAvrchmSettingsColumn(idx, 'left')}
            onRightClick={() => moveAvrchmSettingsColumn(idx, 'right')}
            onEditClick={() => handleEditClick(idx, 'start', newColumn)}
            onRemoveClick={() => removeAvrchmSettingsColumn(idx)}
          />
        ),
      })
      newColumnSearchDisabled.splice(idx + 1, 0, String(idx + 1))
      newRows = newRows.map((row) => {
        let varName: InitialRow['varName'] | undefined = row.varName
        if (row.varName === 'parameter') {
          switch (newColumn.source) {
            case CalcModelAvrchmSettingsSource.NEPTUNE:
              varName = 'plant'
              break
            case CalcModelAvrchmSettingsSource.MODES:
              varName = 'marketCalcModelId'
              break
            case CalcModelAvrchmSettingsSource.FORMULA:
              varName = 'formula'
              break
            default:
              varName = undefined
          }
        }
        let value = varName !== undefined && varName !== 'parameter' ? newColumn[varName] : ''
        if (varName === 'source') {
          value = CalcModelAvrchmSettingsSourceName[newColumn[varName]]
        } else if (varName === 'plant') {
          value = newColumn[varName]?.name
        }

        return {
          ...row,
          [idx + 1]: value,
        }
      })
    })
    setColumns(newColumns)
    setRows(newRows)
    setColumnSearchDisabled(newColumnSearchDisabled)
  }, [avrchmSettings, avrchmSettingsTargetDate])

  return { columns, rows, columnSearchDisabled, openModal, setOpenModal, updateColumnWidth }
}

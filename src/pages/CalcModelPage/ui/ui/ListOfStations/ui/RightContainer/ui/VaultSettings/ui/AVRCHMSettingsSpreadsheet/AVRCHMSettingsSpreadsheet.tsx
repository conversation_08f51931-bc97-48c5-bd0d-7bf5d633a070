import Handsontable from 'handsontable'
import { observer } from 'mobx-react'
import { getStyledComment } from 'pages/CalculationsPage/ui/StationBody/lib'
import { PageLoader } from 'shared/ui/PageLoader/PageLoader'
import { useStore } from 'stores/useStore.ts'
import { validateSpreadsheetFormula } from 'widgets/Spreadsheet/ui/lib'
import { SpreadsheetReact } from 'widgets/SpreadsheetReact'

import cls from './AVRCHMSettingsSpreadsheet.module.scss'

export const AVRCHMSettingsSpreadsheet = observer(() => {
  const { calcModelStore } = useStore()
  const { listOfStationsStore } = calcModelStore
  const { avrchmSpreadsheet, avrchmSpreadsheetDataLoaded } = listOfStationsStore
  const { columns, data, nestedHeaders, cell, rowHeaders } = avrchmSpreadsheet

  /**
   * Подкрашиваем жирным первую строку заголовков столбцов
   * @param col - Индекс столбца.
   * @param th - Элемент заголовка столбца.
   */
  const afterGetColHeader: Handsontable.GridSettings['afterGetColHeader'] = (col, th) => {
    const headerText = col === -1 ? 'Час' : (th.textContent ?? '')

    if (col !== -1) {
      th.classList.add(cls.bold)
    }
    th.innerHTML = `
      <div>
        <span style="line-height: 18px; height: 18px">
          ${headerText}
        </span>
      </div>
    `
  }

  const beforeRenderer: Handsontable.GridSettings['beforeRenderer'] = (TD, _, __, ___, value, cellProperties) => {
    const message = validateSpreadsheetFormula(value)
    if (message) {
      TD.classList.add('isNotValid')
      cellProperties.comment = getStyledComment('Некорректная формула')
    }
  }

  if (!avrchmSpreadsheetDataLoaded) {
    return <PageLoader />
  }

  return (
    <div className={cls.container}>
      <SpreadsheetReact
        height='415px'
        maxRows={data.length > 0 ? data.length : undefined}
        enableFormulasPlugin
        columns={columns}
        data={data}
        nestedHeaders={nestedHeaders}
        cell={cell}
        rowHeaders={rowHeaders}
        afterGetColHeader={afterGetColHeader}
        beforeRenderer={beforeRenderer}
      />
    </div>
  )
})

import { format } from 'date-fns'
import { observer } from 'mobx-react'
import { useTableData } from 'pages/CalcModelPage/ui/ui/ListOfStations/ui/RightContainer/ui/VaultSettings/ui/AVRCHMSettingsModal/lib'
import { useEffect } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { Button } from 'shared/ui/Button'
import { SubtitleWithActions } from 'shared/ui/SubtitleWithActions'
import { useStore } from 'stores/useStore.ts'
import { Table } from 'widgets/Table'

import { AVRCHMSettingsModal } from './ui/AVRCHMSettingsModal'
import { AVRCHMSettingsSpreadsheet } from './ui/AVRCHMSettingsSpreadsheet'
import cls from './VaultSettings.module.scss'

interface VaultSettingsProps {
  editModeRole: boolean
}

export const VaultSettings = observer((props: VaultSettingsProps) => {
  const { editModeRole } = props
  const { calcModelStore } = useStore()
  const { date, listOfStationsStore } = calcModelStore
  const { initAvrchmSettings, resetAvrchmSettings, saveAvrchmSettings, isAvrchmSettingsChanged } = listOfStationsStore
  const { columns, rows, columnSearchDisabled, setOpenModal, openModal, updateColumnWidth } = useTableData(editModeRole)

  useHotkeys('ctrl+shift+s', () => isAvrchmSettingsChanged && saveAvrchmSettings())
  useHotkeys('ctrl+shift+x', () => isAvrchmSettingsChanged && resetAvrchmSettings())

  useEffect(() => {
    initAvrchmSettings(format(date, 'yyyy-MM-dd'))
  }, [date])

  const handleClose = () => {
    setOpenModal(false)
  }

  return (
    <div className={cls.container}>
      <SubtitleWithActions
        title='Настройка страницы Свод'
        actions={[
          <Button disabled={false} variant='outlined' onClick={resetAvrchmSettings}>
            Сбросить
          </Button>,
          <Button disabled={false} onClick={saveAvrchmSettings}>
            Сохранить
          </Button>,
        ]}
        isActionsVisible={isAvrchmSettingsChanged}
      />
      <div className={cls.body}>
        <h3 className={cls.subtitle}>Настройка таблицы АВРЧМ</h3>
        <Table
          disabledDragAndDrop
          columns={columns}
          rows={rows}
          height={165}
          columnSearchDisabled={columnSearchDisabled}
          onColumnWidthsChange={updateColumnWidth}
        />
        <AVRCHMSettingsSpreadsheet />
      </div>
      {openModal && <AVRCHMSettingsModal onClose={handleClose} />}
    </div>
  )
})

import { Menu, MenuItem } from '@mui/material'
import { MouseEvent, useEffect, useMemo, useState } from 'react'
import { Button } from 'shared/ui/Button'
import { FormField } from 'shared/ui/FormField'
import { TextField } from 'shared/ui/TextField'
import { parseNumberWithComma } from 'shared/ui/TextField/lib'
import { TextFieldProps } from 'shared/ui/TextField/model/types'
import { useStore } from 'stores/useStore.ts'

import { bracketsFormulaList, formulaRegex, IFormulaListItem, kFormulaList } from '../../lib'
import cls from './FormulaField.module.scss'

interface FormulaFieldProps {
  className: string
  fieldClassName: string
  labelClassName: string
  error: TextFieldProps['error']
  helperText: TextFieldProps['helperText']
  onChange: (value: string) => void
  value?: string
}

export const FormulaField = (props: FormulaFieldProps) => {
  const { className, fieldClassName, labelClassName, error, helperText, onChange, value } = props
  const [anchorRef, setAnchorRef] = useState<HTMLButtonElement | null>(null)
  const [formula, setFormula] = useState<string>('')
  const [list, setList] = useState<IFormulaListItem[]>([])
  const { calcModelStore } = useStore()
  const { listOfStationsStore } = calcModelStore
  const { avrchmSettings } = listOfStationsStore

  const kList = useMemo(() => kFormulaList(avrchmSettings.columns), [avrchmSettings])

  const bracketsList = useMemo(() => bracketsFormulaList(avrchmSettings.columns), [avrchmSettings])

  useEffect(() => {
    setFormula(value ?? '')
  }, [value])

  const handleChangeFormula: TextFieldProps['onChange'] = (e) => {
    const value = parseNumberWithComma(e.target.value)
    if (formulaRegex.test(value)) {
      if (e.target.value.includes(',')) {
        const caretPos = e.target.value.indexOf(',') + 1
        e.target.value = value
        e.target.setSelectionRange(caretPos, caretPos)
      } else {
        e.target.value = value
      }
      setFormula(value)
      onChange(value)
    }
  }

  const handleButtonClick = (button: 'k' | 'brackets', e: MouseEvent<HTMLButtonElement>) => {
    if (button === 'k') {
      setAnchorRef(e.currentTarget)
      setList(kList)
    } else {
      setAnchorRef(e.currentTarget)
      setList(bracketsList)
    }
  }

  const handleClose = () => {
    setAnchorRef(null)
  }

  const updateFormula = (value: string) => {
    const newFormula = (formula || '') + value
    setFormula(newFormula)
    onChange(newFormula)
    handleClose()
  }

  return (
    <FormField
      className={className}
      fieldClassName={fieldClassName}
      labelClassName={labelClassName}
      label={
        <div>
          Формула
          <div className={cls.labelBottomMargin} />
        </div>
      }
      field={
        <div>
          <TextField
            type='text'
            multiline
            value={formula}
            onChange={handleChangeFormula}
            className={cls.textField}
            error={error}
            helperText={helperText}
          />
          <div className={cls.actions}>
            <Button variant='outlined' onClick={(e) => handleButtonClick('brackets', e)}>
              [ ]
            </Button>
            <Button variant='outlined' onClick={(e) => handleButtonClick('k', e)}>
              K
            </Button>
            <Menu
              id='basic-menu'
              anchorEl={anchorRef}
              open={!!anchorRef}
              onClose={handleClose}
              MenuListProps={{
                'aria-labelledby': 'basic-button',
              }}
            >
              {list.map((item) => (
                <MenuItem onClick={() => updateFormula(item.value)}>{item.label}</MenuItem>
              ))}
            </Menu>
          </div>
        </div>
      }
    />
  )
}

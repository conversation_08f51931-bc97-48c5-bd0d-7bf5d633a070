import { EmptyFunction } from 'entities/shared/common.entities.ts'
import { TableHeaderActionButton } from 'widgets/Table'

import cls from './TableHeaderCell.module.scss'

interface TableHeaderCellProps {
  title: string
  hideLeftArrow: boolean
  hideRightArrow: boolean
  hideActions: boolean
  disableActions: boolean
  onLeftClick: EmptyFunction<void>
  onRightClick: EmptyFunction<void>
  onEditClick: EmptyFunction<void>
  onRemoveClick: EmptyFunction<void>
}

export const TableHeaderCell = (props: TableHeaderCellProps) => {
  const {
    title,
    hideLeftArrow,
    hideRightArrow,
    hideActions,
    disableActions,
    onLeftClick,
    onRightClick,
    onEditClick,
    onRemoveClick,
  } = props

  return (
    <div className={cls.tableHeaderCell}>
      {title}
      <div className={cls.actionHeader}>
        {hideActions ||
          (!hideLeftArrow && (
            <TableHeaderActionButton
              disabled={disableActions}
              tooltip='Сместить влево'
              color='primary'
              iconName='arrowLeft'
              onClick={onLeftClick}
            />
          ))}
        {hideActions ||
          (!hideRightArrow && (
            <TableHeaderActionButton
              disabled={disableActions}
              tooltip='Сместить вправо'
              color='primary'
              iconName='arrow'
              onClick={onRightClick}
            />
          ))}
        {hideActions || (
          <TableHeaderActionButton
            disabled={disableActions}
            tooltip='Отредактировать'
            color='primary'
            iconName='settings'
            onClick={onEditClick}
          />
        )}
        {hideActions || (
          <TableHeaderActionButton
            disabled={disableActions}
            tooltip='Удалить'
            color='red'
            iconName='trash'
            onClick={onRemoveClick}
          />
        )}
      </div>
    </div>
  )
}

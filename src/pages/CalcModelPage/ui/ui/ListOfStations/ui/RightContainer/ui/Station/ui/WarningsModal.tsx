import { PriorityHigh } from '@mui/icons-material'
import { TStatus } from 'entities/api/nsiManager.entities.ts'
import { observer } from 'mobx-react'
import { FC, useState } from 'react'
import { LoadingButton } from 'shared/ui/LoadingButton'
import { Modal } from 'shared/ui/Modal'
import { useStore } from 'stores/useStore.ts'

import cls from './WarningsModal.module.scss'

interface WarningsModalProps {
  reinitDataAfterSave: (plantId: number, date: string) => void
}

export const WarningsModal: FC<WarningsModalProps> = observer((props) => {
  const { reinitDataAfterSave } = props
  const { calcModelStore } = useStore()
  const { warningsAfterSaving, clearWarningsAfterSaving, saveParams } = calcModelStore
  const [isSaved, setIsSaved] = useState<TStatus>('DONE')

  const handleSave = async () => {
    if (warningsAfterSaving?.requestParams) {
      setIsSaved('IN_PROCESS')
      try {
        await saveParams(warningsAfterSaving.requestParams, warningsAfterSaving.date, true)
        setIsSaved('DONE')
        clearWarningsAfterSaving()
        reinitDataAfterSave(warningsAfterSaving.requestParams.id, warningsAfterSaving.date)
      } catch {
        setIsSaved('DONE')
      }
    }
  }

  return (
    <Modal
      skipConfirmOnClose
      title='Сохранение - предупреждения и ошибки'
      open={!!warningsAfterSaving?.warnings}
      onClose={clearWarningsAfterSaving}
      className={cls.AcceptModal}
      actions={
        <div className={cls.buttonsContainer}>
          <LoadingButton
            variant='contained'
            onClick={handleSave}
            loading={isSaved === 'IN_PROCESS'}
            className={cls.continueButton}
          >
            Сохранить
          </LoadingButton>
        </div>
      }
    >
      {warningsAfterSaving?.warnings.map((err, index) => {
        return (
          <div className={cls.errorMessageContainer} key={index}>
            <div className={cls.icon}>
              <PriorityHigh />
            </div>
            <div className={cls.errorMessage}>{err}</div>
          </div>
        )
      })}
    </Modal>
  )
})

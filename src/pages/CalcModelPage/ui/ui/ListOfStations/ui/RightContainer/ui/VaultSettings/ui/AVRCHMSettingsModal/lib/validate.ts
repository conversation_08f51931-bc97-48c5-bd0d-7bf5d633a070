import { CalcModelAvrchmSettingsSource, ICalcModelAvrchmSettings } from 'entities/store/calcModelStore.entities.ts'

export interface AvrchmSettingsFormErrors {
  title?: string | undefined
  formula?: string | undefined
  plant?: string | undefined
  marketCalcModelId?: string | undefined
}

export const validate = (avrchmColumnSettings: ICalcModelAvrchmSettings['columns'][0]): AvrchmSettingsFormErrors => {
  const errors: AvrchmSettingsFormErrors = {}
  if (!avrchmColumnSettings.title.trim()) {
    errors['title'] = 'Поле должно быть заполнено'
  }
  if (avrchmColumnSettings.source === CalcModelAvrchmSettingsSource.FORMULA && !avrchmColumnSettings.formula?.trim()) {
    errors['formula'] = 'Поле должно быть заполнено'
  }
  if (avrchmColumnSettings.source === CalcModelAvrchmSettingsSource.NEPTUNE && !avrchmColumnSettings.plant?.name) {
    errors['plant'] = 'Поле должно быть заполнено'
  }
  if (
    avrchmColumnSettings.source === CalcModelAvrchmSettingsSource.MODES &&
    avrchmColumnSettings.marketCalcModelId === undefined
  ) {
    errors['marketCalcModelId'] = 'Поле должно быть заполнено'
  }

  return errors
}

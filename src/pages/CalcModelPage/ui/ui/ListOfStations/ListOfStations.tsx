import { Tooltip } from '@mui/material'
import { format, isValid } from 'date-fns'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { observer } from 'mobx-react'
import { SettingsModal } from 'pages/CalcModelPage/ui/ui/SettingsModal'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { classNames } from 'shared/lib/classNames/classNames'
import { locationParse } from 'shared/lib/locationParse'
import { Button } from 'shared/ui/Button'
import { CheckEditComponent } from 'shared/ui/CheckEditComponent'
import { DatePicker } from 'shared/ui/DatePicker'
import { Icon } from 'shared/ui/Icon'
import { MenuDragAndDrop } from 'shared/ui/MenuDragAndDrop'
import { CurrentItemProps } from 'shared/ui/MenuDragAndDrop/MenuDragAndDrop'
import { SettingStationsForWatch } from 'shared/ui/SettingStationsForWatch/SettingStationsForWatch.tsx'
import { TextField } from 'shared/ui/TextField'
import { IPlantForLeftMenu, IPrepareRgu } from 'stores/CalcModelStore'
import { useStore } from 'stores/useStore'

import cls from './ListOfStations.module.scss'
import { RightContainer } from './ui/RightContainer'

export interface IRow extends Omit<Omit<IPrepareRgu, 'children'>, 'generators'> {
  generators: string[]
  tabId: string
  isEdit: boolean
  id: number
  children: IRow[]
  avrchm: boolean
  voltage: string
}

const isEditing = (arr: IRow[]): boolean => {
  return arr.some((item) => {
    if (item.isEdit) return true
    else if (item.children) return isEditing(item.children)

    return false
  })
}

export type TSort = 'alphabeat' | 'custom'

export const ListOfStations = observer(() => {
  const { calcModelStore, authStore } = useStore()
  const { userDetail } = authStore
  const { roles } = userDetail
  const accessRole = [ROLES.TECH_ADMIN_CM]
  const {
    date,
    initListOfStations,
    changeSortType,
    setCustomSortLeft,
    depPlants,
    initModalAdd,
    lookedPlants,
    resetLookedPlants,
    saveDepPlants,
    editInMixingById,
    saveModesRecForId,
    plants,
  } = calcModelStore
  const history = useNavigate()
  const [search, setSearch] = useState('')
  const [settingsModal, setSettingsModal] = useState<CurrentItemProps | null>(null)
  const [editMode, setEditMode] = useState(false)
  const [rows, setRows] = useState<IPrepareRgu[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [errors, setErrors] = useState(new Map())
  const [isLoadingTable, setIsLoadingTable] = useState(true)
  const [finalPlants, setFinalPlants] = useState<IPlantForLeftMenu[]>([])
  const [isAddModal, setIsAddModal] = useState(false)
  const [selectLeftMenu, setSelectLeftMenu] = useState<number>(0)
  const [typeSort, setTypeSort] = useState<TSort>('custom')
  const isEditRows = isEditing(rows as unknown as IRow[])

  const init = (newDate?: Date) => {
    const d = newDate || date

    if (d) {
      const year = d.getFullYear()
      const month = d.getMonth() + 1 > 9 ? d.getMonth() + 1 : `0${d.getMonth() + 1}`
      const day = d.getDate() > 9 ? d.getDate() : `0${d.getDate()}`
      const prepareDate = `${year}-${month}-${day}`
      initListOfStations(prepareDate)
    }
  }

  const getDataLeft = (newDate?: Date) => {
    const d = newDate || date
    if (d) {
      const prepareDate = format(new Date(d), 'yyyy-MM-dd')

      if (typeSort === 'alphabeat') {
        changeSortType(false, prepareDate)
      } else {
        changeSortType(true, prepareDate)
      }
    }
  }

  const handleDateChange = (date: Date) => {
    if (!isValid(date)) return
    calcModelStore.changeDate(date)
    history(`?year=${date.getFullYear()}&month=${date.getMonth() + 1}&day=${date.getDate()}`)
    init(date)
    getDataLeft(date)
  }

  const [data, setData] = useState<IPlantForLeftMenu[]>([])

  useEffect(() => {
    setData(plants)
  }, [plants])

  useEffect(() => {
    const { year = null, month = null, day = null } = locationParse(location.search)
    const nowDate = new Date()
    nowDate.setDate(nowDate.getDate() + 1)
    const initDate = year && month && day ? new Date(`${year}-${month}-${day}`) : nowDate
    calcModelStore.changeDate(initDate)
    init()

    return () => {
      calcModelStore.resetPlants()
      calcModelStore.resetStore()
    }
  }, [])

  useEffect(() => {
    const vaultItem = {
      label: 'Свод',
      value: 0,
      order: -1,
    } as IPlantForLeftMenu
    setFinalPlants(() => {
      return [
        vaultItem,
        ...data.filter((el) => {
          return el?.label?.toUpperCase()?.includes(search?.toUpperCase())
        }),
      ]
    })
  }, [data, search])

  useEffect(() => {
    getDataLeft()
  }, [typeSort])

  const editModeRole = roles
    .map((el) => el.role)
    .some((el) => {
      return accessRole.some((item) => item === el)
    })

  const customCell = (item: CurrentItemProps) => {
    const isSettings = item?.icon === 'settings'
    const isView = item?.icon === 'view'

    return (
      <div
        className={classNames(
          cls.DragMenuCell,
          {
            [cls.Settings]: isSettings,
            [cls.View]: isView,
            [cls.Select]: selectLeftMenu === item?.value,
          },
          [],
        )}
      >
        <div className={classNames(cls.CustomLabel, {}, [])}>
          <Tooltip title={item?.label?.length > 19 ? item?.label : null}>
            <>{item?.label}</>
          </Tooltip>
        </div>
        {editModeRole && item.value !== 0 && (
          <div className={classNames(cls.Action, {}, [])}>
            <Button
              variant='text'
              className={classNames(cls.IconContainer, {}, [])}
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                setSettingsModal(item)
              }}
            >
              <Icon width={18} name='settings' />
            </Button>
          </div>
        )}
      </div>
    )
  }
  const propsRight = {
    editMode,
    setEditMode,
    rows,
    setRows,
    isLoading,
    setIsLoading,
    errors,
    setErrors,
    isLoadingTable,
    setIsLoadingTable,
    finalPlants,
    selectLeftMenu,
    isEditRows,
    init,
    editModeRole,
    plants: data,
  }

  return (
    <CheckEditComponent isEdit={editMode || isEditRows}>
      <div className={classNames(cls.ListOfStations, {}, [])}>
        <div className={classNames(cls.Left, {}, [])}>
          <div className={classNames(cls.HeaderLeft, {}, [])}>
            <DatePicker
              isDayOfWeek
              isArrow
              className={classNames(cls.DatePicker, {}, [])}
              disabled={editMode || isEditRows}
              value={date}
              setValue={handleDateChange}
            />
            {editModeRole && (
              <Button
                className={cls.ButtonSettingStation}
                variant='outlined'
                onClick={() => {
                  setIsAddModal(true)
                }}
              >
                Настройка станций
              </Button>
            )}
          </div>
          <div className={classNames(cls.SearchContainer, {}, [])}>
            <TextField
              className={classNames(cls.SearchInput, {}, [])}
              variant='standard'
              value={search}
              placeholder='Поиск'
              onChange={(e) => {
                setSearch(e.target.value)
              }}
            />
            <div className={classNames(cls.Buttons, {}, [])}>
              <Button
                variant='text'
                className={classNames(
                  cls.SearchButton,
                  {
                    [cls.SelectSort]: typeSort === 'alphabeat',
                    [cls.UnSelectSort]: typeSort === 'custom',
                  },
                  [],
                )}
                onClick={() => {
                  setTypeSort('alphabeat')
                }}
              >
                <Icon width={18} name='sortAlphabeat' />
              </Button>
              <Button
                variant='text'
                className={classNames(
                  cls.SearchButton,
                  {
                    [cls.SelectSort]: typeSort === 'custom',
                    [cls.UnSelectSort]: typeSort === 'alphabeat',
                  },
                  [],
                )}
                onClick={() => {
                  setTypeSort('custom')
                }}
              >
                <Icon width={18} name='sortCustom' />
              </Button>
            </div>
          </div>
          <div className={classNames(cls.DragAndDropContainer, {}, [])}>
            <MenuDragAndDrop
              isViewActions={false}
              select={selectLeftMenu}
              isEdit={editMode || isEditRows}
              setSelect={(v) => {
                setIsLoading(true)
                setIsLoadingTable(true)
                setErrors((prev) => {
                  prev.clear()

                  return prev
                })
                setSelectLeftMenu(v)
                setEditMode(false)
              }}
              items={finalPlants as CurrentItemProps[]}
              customCell={customCell}
              disabled={typeSort === 'alphabeat' || !editModeRole}
              typeSort={typeSort}
              isDragMode
              onChangePosition={async (res) => {
                await setCustomSortLeft(res.filter((el) => el !== '0'))
              }}
            />
          </div>
        </div>
        <RightContainer {...propsRight} />
        {isAddModal && (
          <SettingStationsForWatch
            onClose={() => {
              setIsAddModal(false)
            }}
            onSave={() => {
              setIsAddModal(false)
              init()
              getDataLeft()
            }}
            depPlants={depPlants}
            initModalAdd={initModalAdd}
            lookedPlants={lookedPlants}
            resetLookedPlants={resetLookedPlants}
            saveDepPlants={saveDepPlants}
          />
        )}
        {settingsModal && (
          <SettingsModal
            object={
              settingsModal as unknown as {
                value: number | null
                label: string
                viewOnly: boolean
                record: boolean
                mixing: boolean
              }
            }
            onClose={() => {
              setSettingsModal(null)
            }}
            onConfirm={(plantId: number, values: { mixing: boolean; record: boolean }) => {
              const arrPromise = [editInMixingById(plantId, values.mixing)]
              if (!settingsModal.viewOnly) arrPromise.push(saveModesRecForId(plantId, values.record))

              void Promise.allSettled(arrPromise).then(() => {
                setSettingsModal(null)
              })
            }}
          />
        )}
      </div>
    </CheckEditComponent>
  )
})

import s from '../ui/Generalcalculationparameters.module.scss'

export const getStyleStage = (category: 'VSVGO' | 'PSV' | 'PSV') => {
  switch (category) {
    case 'VSVGO':
      return s.stageBlockVSVGO
    default:
      return s.stageBlockOther
  }
}

export const getStartTime = (time: string | undefined): string | null => {
  if (!time) return null

  const [hours, minutes] = time.split(':')
  if (+minutes < 9) {
    return `${hours}:0${+minutes + 1}`
  } else if (+minutes === 59) {
    return `${+hours > 8 ? +hours + 1 : `0${+hours + 1}`}:00`
  } else return `${hours}:${+minutes + 1}`
}

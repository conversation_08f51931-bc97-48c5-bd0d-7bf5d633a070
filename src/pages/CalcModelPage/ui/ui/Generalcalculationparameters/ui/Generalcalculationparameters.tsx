import { GeneralParameter } from 'entities/api/calcModelPage.entities.ts'
import { TIME_LOADER } from 'entities/constants.ts'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { IUserDetails } from 'entities/store/auth.entities.ts'
import { useEffect, useMemo, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import api from 'shared/api/index'
import { classNames } from 'shared/lib/classNames/classNames'
import { generateUUID } from 'shared/lib/GenerationUUID'
import { Button } from 'shared/ui/Button'
import { CheckEditComponent } from 'shared/ui/CheckEditComponent'
import { SubtitleWithActions } from 'shared/ui/SubtitleWithActions'
import { Switch } from 'shared/ui/Switch'
import { Table } from 'widgets/Table'

import { getStartTime, getStyleStage } from '../lib'
import s from './Generalcalculationparameters.module.scss'
import { Sausage } from './Sausage/Sausage'

export interface GeneralcalculationparametersProps {
  userDetail: IUserDetails
}

export interface IRow extends GeneralParameter {
  order: number
  isEdit: boolean
  active: boolean
  tabId: number
  offsetDays: number
  tempEdits: number[]
}

const Generalcalculationparameters = (props: GeneralcalculationparametersProps) => {
  const { userDetail } = props
  const accessRole = [ROLES.TECH_ADMIN_CM]

  const { roles } = userDetail
  const editMode = roles
    ? roles
        .map((el) => el.role)
        .some((el: string) => {
          return accessRole.some((item) => item === el)
        })
    : false
  const [data, setData] = useState<IRow[]>([])
  const [rows, setRows] = useState<IRow[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const column = [
    {
      name: 'stage',
      title: 'Этап',
      width: 150,
      render: (value: { title: string }, row: IRow) => {
        return (
          <div
            data-id={`${row.stage.code.toLowerCase()}_stage`}
            className={`${s.stageBlock} ${row.active ? getStyleStage(row.category.code) : ''}`}
          >
            {value.title}
          </div>
        )
      },
    },
    {
      name: 'startTime',
      title: 'Регламент начало',
      width: 250,
      render: (_: unknown, row: IRow) => {
        if (!row.active) return <></>

        const getPrevRow = (order: number): IRow | null => {
          const row = rows.find((item) => item.order === order - 1)
          if (row) {
            if (row.active) return row
            else return getPrevRow(order - 1)
          } else return null
        }

        const prevRow = getPrevRow(row.order)

        return (
          <>
            {prevRow ? (
              <div data-id={`${row.stage.code.toLowerCase()}_reglament_start`} className={s.tdRow}>
                <div className={classNames(s.stageBlock, {}, [s.stageTime])}>X-{prevRow?.offsetDays}</div>
                {getStartTime(prevRow?.endTime)}
              </div>
            ) : (
              <></>
            )}
          </>
        )
      },
    },
    {
      name: 'endTime',
      title: 'Регламент конец',
      width: 250,
      editingEnabled: (row: IRow) => !!row.active,
      editType: 'time',
      render: (value: string, row: IRow) => {
        if (!row.active) return <></>

        return (
          <div data-id={`${row.stage.code.toLowerCase()}_reglament_end`} className={s.tdRow}>
            <div className={classNames(s.stageBlock, {}, [s.stageTime])}>X-{row.offsetDays}</div>
            {value}
          </div>
        )
      },
    },
    {
      name: 'offsetDays',
      title: 'Смещение в днях',
      width: 150,
      editingEnabled: (row: IRow) => !!row.active,
      editType: 'number',
      numberOption: {
        positive: true,
        isInteger: true,
        max: 10,
      },
      render: (value: string, row: IRow) => {
        if (!row.active) return <></>

        return <span data-id={`${row.stage.code.toLowerCase()}_offset`}>{value}</span>
      },
    },
    {
      name: 'active',
      title: 'Расчет',
      headRender: () => {
        return <div className={s.ActionHeader}>Расчет</div>
      },
      width: 100,
      render: (value: boolean, row: IRow) => {
        return (
          <div data-id={`${row.stage.code.toLowerCase()}_toggle`} className={s.IconCell}>
            <Switch
              disabled={!editMode}
              checked={value}
              onChange={() => {
                handleChangeActive(row.order)
              }}
            />
          </div>
        )
      },
    },
  ]

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)
      try {
        const data = await api.calcModelManager.getGeneralParameters()
        const tableData = data.map((item: GeneralParameter) => ({
          ...item,
          tabId: generateUUID(),
        }))
        setData(tableData as unknown as IRow[])
        setRows(tableData as unknown as IRow[])
      } catch (error) {
        console.log(error)
      } finally {
        setTimeout(() => {
          setIsLoading(false)
        }, TIME_LOADER)
      }
    }
    void fetchData()
  }, [])

  const handleChangeData = (rows: IRow[]) => {
    setRows(rows)
  }

  const handleChangeActive = (order: number) => {
    setRows((prev) =>
      prev.map((item) =>
        item.order !== order
          ? item
          : {
              ...item,
              active: !item.active,
              isEdit: true,
            },
      ),
    )
  }

  const handleReset = () => {
    setRows(data)
  }

  const handleSave = async () => {
    const params = rows
      .filter((item) => item.isEdit)
      .map((item) => ({
        stage: item.stage,
        timeEnd: item.endTime,
        offsetDays: item.offsetDays,
        active: item.active,
      }))

    try {
      const data = await api.calcModelManager.editGeneralParameters(params)
      const tableData = data.map((item: GeneralParameter) => ({
        ...item,
        tabId: generateUUID(),
      }))
      setData(tableData as unknown as IRow[])
      setRows(tableData as unknown as IRow[])
    } catch (error) {
      console.log(error)
    }
  }

  const edited = useMemo(() => {
    return rows.some((item) => item.isEdit)
  }, [rows])

  useHotkeys('ctrl+shift+s', () => edited && handleSave())
  useHotkeys('ctrl+shift+x', () => edited && handleReset())

  const isEdit = rows.some((el) => el?.tempEdits?.length > 0)

  return (
    <CheckEditComponent isEdit={edited || isEdit}>
      <div className={s.container}>
        <SubtitleWithActions
          title='Настройка этапов планирования'
          actions={
            editMode
              ? [
                  <Button disabled={!edited} variant='outlined' onClick={handleReset}>
                    Сбросить
                  </Button>,
                  <Button disabled={!edited} onClick={handleSave}>
                    Сохранить
                  </Button>,
                ]
              : []
          }
          isActionsVisible
        />
        <div className={s.wrapper}>
          <Table
            loading={isLoading}
            initialData={data}
            rows={rows}
            columns={column}
            editMode={editMode}
            showSortingControls={false}
            showSearchControls={false}
            setRows={(rows) => {
              handleChangeData(rows as IRow[])
            }}
          />
        </div>
        <Sausage rows={rows} />
      </div>
    </CheckEditComponent>
  )
}

export default Generalcalculationparameters

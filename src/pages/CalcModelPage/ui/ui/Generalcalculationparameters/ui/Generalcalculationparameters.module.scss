.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0.2rem;
}

.Switcher {
  width: 63%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wrapper {
  display: flex;
  justify-content: space-around;
  gap: 3em;
}

.stageBlock {
  padding: 0.1em 0.75em;
  border-radius: 4px;
  color: #fff;
  background-color: #ccc;
  font-size: 0.75rem;

  &VSVGO {
    background-color: #394;
  }

  &Other {
    background-color: #0263d9;
  }
}

.stageTime {
  color: var(--text-color);
}

.tdRow {
  display: flex;
  align-items: center;
  gap: 0.5em;
}

.ActionHeader {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.IconCell {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;

  label {
    margin-right: 0 !important;
  }
}

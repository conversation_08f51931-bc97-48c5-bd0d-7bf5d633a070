@mixin arrow {
  content: ">";
  position: absolute;
  color: #949494;
  font-size: 2em;
}

.sausage {
  display: flex;
  align-items: flex-end;
  gap: 2em;
  margin-top: auto;
  padding-top: 2em;
}

.stageTime {
  color: var(--text-color);
}

.item {
  position: relative;
  order: 1;
  max-width: 140px;
  width: 100%;
  height: fit-content;
  padding: 0.5em 1em;
  border-radius: 10px;
  color: #fff;
  text-align: center;

  & + .item:not(.noActive)::after {
    @include arrow;
    left: -0.75em;
  }

  &.noActive {
    order: 0;
    background-color: #ccc;
    color: rgba(0, 0, 0, 0);
  }
}

.info {
  order: 2;
  text-align: center;
  color: var(--text-color);
}

.tdRow {
  display: flex;
  align-items: center;
  gap: 0.5em;
  position: absolute;
  top: -30px;
  right: -50px;
}

.stageBlock {
  padding: 0.1em 0.75em;
  border-radius: 4px;
  color: var(--text-color);
  background-color: #ccc;
  font-size: 0.75rem;

  &VSVGO {
    background-color: #394;
  }

  &Other {
    background-color: #0263d9;
  }
}

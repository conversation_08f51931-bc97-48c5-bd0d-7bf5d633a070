import { GeneralParameter } from 'entities/api/calcModelPage.entities.ts'

import { getStyleStage } from '../../lib'
import s from './Sausage.module.scss'

interface Props {
  rows: GeneralParameter[]
}

export const Sausage = (props: Props) => {
  const { rows } = props

  return (
    <>
      {!!rows.length && (
        <div className={s.sausage}>
          {rows.map((item: GeneralParameter) => (
            <div
              key={item.stage.code}
              className={`${s.item} ${item.active ? getStyleStage(item.category.code) : s.noActive}`}
            >
              {item.active ? (
                <div className={s.tdRow}>
                  <div className={s.stageBlock}>X-{item.offsetDays}</div>
                  <span className={s.stageTime}>{item.endTime}</span>
                </div>
              ) : null}
              {item.stage.title}
            </div>
          ))}
          <div className={s.info}>
            X<br />
            Планируемый день
          </div>
        </div>
      )}
    </>
  )
}

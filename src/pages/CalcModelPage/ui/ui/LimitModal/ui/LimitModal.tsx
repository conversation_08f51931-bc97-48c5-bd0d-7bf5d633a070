import { IconButton } from '@mui/material'
import { IRestriction } from 'entities/api/calcModelPage.entities.ts'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { observer } from 'mobx-react'
import { type FC, useEffect, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { classNames } from 'shared/lib/classNames/classNames'
import { generateUUID } from 'shared/lib/GenerationUUID'
import { Button } from 'shared/ui/Button'
import { Icon } from 'shared/ui/Icon'
import { Modal } from 'shared/ui/Modal'
import { Select } from 'shared/ui/Select'
import { TextField } from 'shared/ui/TextField'
import { useStore } from 'stores/useStore'
import { Table } from 'widgets/Table'

import { numberOption } from '../model/constants'
import cls from './LimitModal.module.scss'

interface IObject {
  restrictions: IRestriction[]
  mode: string
}

interface IConfirmProps {
  active?: boolean
  code?: number
  limit?: string
  name?: string
}

interface LimitModalProps {
  onClose?: () => void
  object: IObject
  onConfirm?: (res: IConfirmProps[]) => void
  disabled?: boolean
}

export interface IRow {
  tabId: string
  disabledChecked?: boolean
  visibleAndDisabled?: boolean
  isEdit?: boolean
  active?: boolean
  code?: number
  limit?: string
  name?: string
}

export const LimitModal: FC<LimitModalProps> = observer((props) => {
  const { onClose, object, onConfirm, disabled } = props
  const { calcModelStore, authStore } = useStore()
  const { initLimits, restriction } = calcModelStore

  const { userDetail } = authStore
  const { roles } = userDetail
  const accessRole = [ROLES.TECH_ADMIN_CM]

  const editModeRole = roles
    ? roles
        .map((el) => el.role)
        .some((el: string) => {
          return accessRole.some((item) => item === el)
        })
    : false
  const [rows, setRows] = useState<IRow[]>([])
  const [select, setSelect] = useState<string[]>([])

  const initAddParams = { name: ' ', limit: '0' }
  const [addParams, setAddParams] = useState(initAddParams)

  const columns = editModeRole
    ? [
        { name: 'name', title: 'Причина', width: 190 },
        {
          name: 'limit',
          title: 'Значение, МВт',
          width: 190,
          editingEnabled: true,
          editType: 'number',
          numberOption,
        },
        {
          name: 'actions',
          title: ' ',
          width: 100,
          isBlockedSorting: true,
          render: (_: unknown, row: { tabId: number }) => {
            return editModeRole ? (
              <div className={classNames(cls.TrashButtonContainer, {}, [])}>
                <IconButton
                  disabled={disabled}
                  className={classNames(cls.TrashButton, {}, [])}
                  onClick={() => {
                    setRows((prev) => {
                      return prev.filter((el) => el.tabId !== String(row.tabId))
                    })
                    setSelect((prev) => {
                      return prev.filter((el) => el !== String(row.tabId))
                    })
                  }}
                >
                  <Icon width={14} name='trash' />
                </IconButton>
              </div>
            ) : (
              <></>
            )
          },
        },
      ]
    : [
        {
          name: 'name',
          title: 'Причина',
          width: 190,
          editingEnabled: true,
        },
        {
          name: 'limit',
          title: 'Значение , МВт',
          width: 190,
          editingEnabled: true,
          editType: 'number',
          numberOption,
        },
      ]

  useEffect(() => {
    initLimits(object.mode)
  }, [])

  const [initSelect, setInitSelect] = useState<string[]>([])

  useEffect(() => {
    const res = object?.restrictions
      ? object?.restrictions?.map((el) => ({
          ...el,
          tabId: generateUUID(),
        }))
      : []
    const resRows = res.map((el) => ({
      ...el,
      disabledChecked: false,
      visibleAndDisabled: disabled,
    }))
    setRows(resRows)
    setSelect(() => {
      return res?.filter((el) => el.active)?.map((el) => el.tabId)
    })
    setInitSelect(() => {
      return res?.filter((el) => el.active)?.map((el) => el.tabId)
    })
  }, [object])

  const onSave = () => {
    const res = rows?.map((row) => {
      const active = select?.some((el) => el === row.tabId)

      return { active, code: row.code, limit: row.limit, name: row.name }
    })
    onConfirm && onConfirm(res)
  }

  function arrayCompare(_arr1: string[], _arr2: string[]) {
    if (!Array.isArray(_arr1) || !Array.isArray(_arr2) || _arr1.length !== _arr2.length) {
      return false
    }

    // .concat() to not mutate arguments
    const arr1 = _arr1.concat().sort((a, b) => a.localeCompare(b))
    const arr2 = _arr2.concat().sort((a, b) => a.localeCompare(b))

    for (let i = 0; i < arr1.length; i++) {
      if (arr1[i] !== arr2[i]) {
        return false
      }
    }

    return true
  }

  const isOneEdit =
    rows?.some((el) => el.isEdit) || !arrayCompare(select, initSelect) || rows?.length !== object?.restrictions?.length

  const [rest, setRest] = useState<string | null>(null)

  const finalRestriction = restriction?.filter((el) => {
    return !rows?.some((row) => row.code === el.code)
  })

  const handleReset = () => {
    const res = object?.restrictions
      ? object?.restrictions?.map((el) => ({
          ...el,
          tabId: generateUUID(),
        }))
      : []
    const resRows = res.map((el) => ({
      ...el,
      disabledChecked: false,
      visibleAndDisabled: disabled,
    }))
    setRows(resRows)
    setSelect(() => {
      return res?.filter((el) => el.active)?.map((el) => el.tabId)
    })
    setInitSelect(() => {
      return res?.filter((el) => el.active)?.map((el) => el.tabId)
    })
  }

  useHotkeys('ctrl+shift+s', () => isOneEdit && !disabled && onSave())
  useHotkeys('ctrl+shift+x', () => isOneEdit && !disabled && handleReset())

  return (
    <Modal
      onClose={() => {
        if (isOneEdit) {
          const answer = window.confirm('Изменения сохранены не будут. Вы действительно хотите закрыть модальное окно?')
          if (answer && onClose) {
            onClose()
          }
        } else {
          onClose && onClose()
        }
      }}
      title='Список ограничений'
      actions={
        editModeRole && (
          <div className={classNames(cls.Buttons, {}, [])}>
            {isOneEdit && !disabled && (
              <>
                <Button className={classNames(cls.Button, {}, [])} variant='outlined' onClick={handleReset}>
                  Сбросить
                </Button>
                <Button className={classNames(cls.Button, {}, [])} onClick={onSave}>
                  Сохранить
                </Button>
              </>
            )}
          </div>
        )
      }
    >
      <div className={classNames(cls.BodyModal, {}, [])}>
        <div className={classNames(cls.DescriptionModal, {}, [])}>{object.mode === 'min' ? 'Минимум' : 'Максимум'}</div>
        <div className={classNames(cls.FilterRow, {}, [])}>
          {editModeRole && (
            <>
              <Select
                className={classNames(cls.Selected, {}, [])}
                variant='outlined'
                items={finalRestriction}
                onChange={(value) => {
                  setRest(value)
                }}
                disabled={disabled}
                value={rest ?? ''}
              />
              <TextField
                // todo
                value={addParams.limit}
                type='number'
                disabled={disabled}
                numberOption={numberOption}
                onChange={(e) => {
                  setAddParams((prev) => ({
                    ...prev,
                    limit: e.target.value,
                  }))
                }}
                label='МВт'
                className={classNames(cls.TextField, {}, [])}
                toFixed={3}
              />
              <Button
                onClick={() => {
                  const find = restriction.find((item) => item.code === Number(rest))
                  const resAddParams = addParams
                  resAddParams.limit = String(Number(resAddParams.limit))
                  const res = {
                    ...resAddParams,
                    ...find,
                    active: false,
                    disabledChecked: false,
                    visibleAndDisabled: false,
                    tabId: generateUUID(),
                    isEdit: true,
                    name: find?.name,
                  }
                  setRows((prev) => {
                    return [...prev, res]
                  })
                  setRest(null)
                  setAddParams(initAddParams)
                }}
                disabled={rest === null || addParams.limit.length === 0}
              >
                Добавить
              </Button>
            </>
          )}
        </div>
        <div className={classNames(cls.TableModal, {}, [])}>
          <Table
            columns={columns}
            rows={rows}
            setRows={setRows}
            height={415}
            editMode={editModeRole}
            selectMode='many'
            selection={select}
            setSelection={setSelect}
          />
        </div>
      </div>
    </Modal>
  )
})

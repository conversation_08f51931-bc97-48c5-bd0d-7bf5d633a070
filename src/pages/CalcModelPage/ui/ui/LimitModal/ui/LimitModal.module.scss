.LimitModal {
}

.BodyModal {
  width: 538px;
  height: 470px;
  display: flex;
  flex-direction: column;
}

.TableModal {
  width: 100%;
  height: 100%;
}

.DescriptionModal {
  width: 100%;
  height: 20px;
  font-style: normal;
  line-height: normal;
  color: rgb(150 150 150);
  font-size: 1.125rem;
  font-weight: 600;
}

.FilterRow {
  width: 100%;
  height: 25px;
  display: flex;
  justify-content: space-around;
  margin: 10px 0;
}

.Selected {
  width: 170px;
  height: 14px;

  & > div > div {
    // padding: 2px 0 !important;
  }
}

.TextField {
  margin: 0 8px;
  input {
    padding: 0 16px !important;
    height: 25px !important;
  }
}

.TrashButtonContainer {
  width: 100%;
  display: flex;
  justify-content: center;
}

.TrashButton {
  width: 20px !important;
  height: 20px !important;
  min-width: 20px !important;
  min-height: 20px !important;
  border-radius: 100%;
  padding: 0 !important;
  &:hover {
    color: var(--red-color) !important;
  }
}

.Buttons {
  width: 100%;
  height: 43px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.Button {
  margin: 0 10px !important;
}

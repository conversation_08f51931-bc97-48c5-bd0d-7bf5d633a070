import {
  AddConsumptionSchedulesProps,
  IResObj,
  IRowAddAndEditConsumptionSchedules,
} from 'entities/pages/calcModelPage.entities'
import { IGetEnergyDistrictOutput } from 'entities/store/calcModelStore.entities.ts'
import { observer } from 'mobx-react'
import { useEffect, useMemo, useState } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'
import { Button } from 'shared/ui/Button'
import { Modal } from 'shared/ui/Modal'
import { Select } from 'shared/ui/Select'
import { TextField } from 'shared/ui/TextField'
import { IActiveStage } from 'stores/CalcModelStore'
import { useStore } from 'stores/useStore'
import { Table } from 'widgets/Table'

import cls from './AddConsumptionSchedules.module.scss'

export const AddAndEditConsumptionSchedules = observer((props: AddConsumptionSchedulesProps) => {
  const { className, onClose, object, onConfirm } = props
  const { calcModelStore } = useStore()
  const [notation, setNotation] = useState('')
  const [rows, setRows] = useState<IActiveStage[]>([])
  const initNotation = object?.notation ?? ''
  const {
    initAddConsumptionSchedules,
    energyDistrict,
    allStages,
    departmentsLevel,
    loadingTheFormula,
    consumptionSchedules,
  } = calcModelStore
  const terrs: IGetEnergyDistrictOutput[] =
    object.type === 'add'
      ? energyDistrict.filter((el) => {
          return !el.archived && !consumptionSchedules.some((item) => item.energyDistrictIspId === el.ispId)
        })
      : energyDistrict

  useEffect(() => {
    if (object?.notation) {
      setNotation(initNotation)
    }
    initAddConsumptionSchedules(object.type, object?.energyDistrictIspId ?? 0).then((res) => {
      if (res) {
        setRows(res)
      }
    })
  }, [])

  const [selectedEnergyDistrict, setSelectedEnergyDistrict] = useState<string | number | null>(null)

  const columns = [
    { name: 'title', title: 'Этап планирования', width: 200 },
    {
      name: 'titleICP',
      title: 'Этап планирования ИСП',
      width: 230,
      editingEnabled: true,
      editType: 'select',
      chooseItems: allStages,
      render: (value: number) => {
        const currentItem = allStages.find((el) => el.value === value)

        return <>{currentItem?.label ?? ''}</>
      },
      onAfterChange: (value: string, row: IRowAddAndEditConsumptionSchedules) => {
        const districtId = terrs.find((el) => el.value === selectedEnergyDistrict)?.ispId
        if (row.dcLevel && districtId) {
          loadingTheFormula(Number(row.dcLevel), value, districtId).then((formula) => {
            setRows((prev) => {
              return prev.map((el) => {
                if (el.tabId === row.tabId) {
                  return {
                    ...el,
                    formula,
                    isEdit: true,
                    tempEdits: el.tempEdits
                      ? [...el.tempEdits.filter((el) => el !== 'titleICP'), 'formula', 'titleICP']
                      : ['formula', 'titleICP'],
                  }
                }

                return el
              })
            })
          })
        }
      },
    },
    {
      name: 'dcLevel',
      title: 'Уровень ДЦ',
      width: 200,
      editingEnabled: true,
      editType: 'select',
      chooseItems: departmentsLevel,
      onAfterChange: (value: number, row: IRowAddAndEditConsumptionSchedules) => {
        const districtId = terrs.find((el) => el.value === selectedEnergyDistrict)?.ispId
        if (row.titleICP && districtId) {
          loadingTheFormula(value, row.titleICP, districtId).then((formula) => {
            setRows((prev) => {
              return prev.map((el) => {
                if (el.tabId === row.tabId) {
                  return {
                    ...el,
                    formula,
                    isEdit: true,
                    tempEdits: el.tempEdits
                      ? [...el.tempEdits.filter((el) => el !== 'dcLevel'), 'formula', 'dcLevel']
                      : ['formula', 'dcLevel'],
                  }
                }

                return el
              })
            })
          })
        }
      },
      render: (value: number) => {
        const currentItem = departmentsLevel.find((el) => el.value === value)

        return <>{currentItem?.label ?? ''}</>
      },
    },
    {
      name: 'formula',
      title: 'Формула',
      width: 200,
    },
  ]

  useEffect(() => {
    if (terrs?.length > 0) {
      if (object.type === 'add') {
        const [first] = terrs
        setSelectedEnergyDistrict(first.value)
      } else {
        const find = terrs.find((el) => el.ispId === object.energyDistrictIspId)
        setSelectedEnergyDistrict(find?.value ?? null)
      }
    }
  }, [energyDistrict])

  useEffect(() => {
    if (rows?.length > 0) {
      Promise.all(
        rows.map(async (row) => {
          const districtId = terrs.find((el) => el.value === selectedEnergyDistrict)?.ispId
          if (row.dcLevel && row.titleICP && districtId) {
            const formula = await loadingTheFormula(Number(row.dcLevel), String(row.titleICP), districtId)

            return { ...row, formula }
          }

          return row
        }),
      ).then((loadData) => {
        setRows(loadData)
      })
    }
  }, [selectedEnergyDistrict])
  const isEveryRowsFull = rows?.every((el) => !!el?.formula)

  const ispId = terrs?.find((el) => el.value === selectedEnergyDistrict)?.ispId ?? ''

  const isShowBtns = useMemo(() => {
    return rows.some((item) => item.isEdit) || notation !== initNotation
  }, [rows, notation])

  return (
    <Modal
      maxWidth='xl'
      onClose={onClose}
      title={object.type === 'add' ? 'Создание формул для территории' : 'Настройка формул для территории'}
      className={classNames(cls.AddConsumptionSchedules, {}, className ? [className] : [])}
      actions={
        isShowBtns ? (
          <div className={cls.footer}>
            <Button
              disabled={!isEveryRowsFull}
              onClick={() => {
                if (object.type === 'add') {
                  const resObject = {
                    energyDistrictId: selectedEnergyDistrict,
                    notation,
                    consumptionFormulaList: rows.map((el) => {
                      const planingStageIsp = allStages.find((item) => item.value === el.titleICP)?.code ?? ''
                      const departmentLevel = departmentsLevel.find((item) => item.value === el.dcLevel)?.code ?? ''

                      return {
                        planingStage: el.code,
                        planingStageIsp,
                        departmentLevel,
                      }
                    }),
                  }
                  onConfirm && resObject && onConfirm(resObject as IResObj)
                } else {
                  const resObject = {
                    energyDistrictId: selectedEnergyDistrict,
                    notation,
                    consumptionFormulaList: rows.map((el) => {
                      const planingStageIsp = allStages.find((item) => item.value === el.titleICP)?.code ?? ''
                      const departmentLevel = departmentsLevel.find((item) => item.value === el.dcLevel)?.code ?? ''

                      return {
                        planingStage: el?.planingStage?.code,
                        planingStageIsp,
                        departmentLevel,
                      }
                    }),
                  }
                  onConfirm && onConfirm(resObject as IResObj)
                }
              }}
            >
              {object.type === 'add' ? 'Создать' : 'Сохранить'}
            </Button>
          </div>
        ) : (
          <div className={cls.footer}></div>
        )
      }
    >
      <div className={classNames(cls.HeaderModal, {}, [])}>
        <div className={classNames(cls.Row, {}, [])}>
          <div className={classNames(cls.TitleEdit, {}, [])}>Название</div>
          <div>
            <Select
              variant='outlined'
              className={classNames(cls.Selected, {}, [])}
              items={terrs}
              value={selectedEnergyDistrict ? String(selectedEnergyDistrict) : ''}
              onChange={(value) => setSelectedEnergyDistrict(value)}
              disabled={object.type === 'edit'}
            />
          </div>
        </div>
        <div className={classNames(cls.Row, {}, [])}>
          <div className={classNames(cls.TitleEdit, {}, [])}>ID ИСП</div>
          <div className={classNames(cls.ID, {}, [])}>{ispId}</div>
        </div>
        <div className={classNames(cls.Row, {}, [])}>
          <div className={classNames(cls.TitleEdit, {}, [])}>Примечание</div>
          <div>
            <TextField
              value={notation}
              onChange={(e) => setNotation(e.target.value)}
              className={classNames(cls.TextField, {}, [])}
              maxLength={255}
            />
          </div>
        </div>
      </div>
      <Table columns={columns} rows={rows} setRows={setRows} editMode />
    </Modal>
  )
})

.AddConsumptionSchedules {
  & > div > div {
    width: 1536px;
  }
}

.footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 40px;
}

.Row {
  display: flex;
  align-items: center;
  height: 26px;
  margin: 13px 0;
}

.HeaderModal {
  margin-bottom: 30px;
}

.TextField {
  height: 30px;
  width: 218px;

  input {
    padding: 4px 12px !important;
  }
}

.TitleEdit {
  color: var(--text-color);
  font-family: var(--font-family-main);
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  display: flex;
  align-items: center;
  margin-right: 36px;
  width: 90px;
}

.Selected {
  width: 218px;
  height: 30px;

  & > div > div {
    padding: 4px 12px !important;
  }
}

.ID {
  border-radius: 6px;
  border: 1px solid var(--grey-85, rgb(130 130 130 / 85%));
  width: 218px;
  color: var(--text-gray);
  background-color: var(--gray-background);
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

import { observer } from 'mobx-react'
import { useEffect, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { AnyObject, areObjectsEqual } from 'shared/lib/areEqual'
import { classNames } from 'shared/lib/classNames/classNames'
import { Button } from 'shared/ui/Button'
import { Modal } from 'shared/ui/Modal'
import { Switch } from 'shared/ui/Switch'
import { useStore } from 'stores/useStore'

import cls from './SettingsModal.module.scss'

interface SettingsModalProps {
  className?: string
  onClose?: () => void
  object: {
    value: number | null
    label: string
    viewOnly: boolean
    record: boolean
    mixing: boolean
  }
  onConfirm?: (value: number, values: { record: boolean; mixing: boolean }) => void
}

const INITIAL_VALUES = {
  record: false,
  mixing: false,
}

export interface Promises {
  record?: boolean
  mixing?: boolean
}

export const SettingsModal = observer((props: SettingsModalProps) => {
  const { className, onClose, object, onConfirm } = props
  const { calcModelStore } = useStore()
  const { getModesRecForId, getInMixingById } = calcModelStore
  const [originalValues, setOriginalValues] = useState({ ...INITIAL_VALUES })
  const [values, setValue] = useState(INITIAL_VALUES)
  const [isVisibleActionBtns, setIsVisibleActionBtns] = useState(false)

  useEffect(() => {
    const id = object?.value ?? 0
    const arrPromise: Promise<Promises>[] = [getInMixingById(id) as unknown as Promise<Promises>]
    if (!object.viewOnly) {
      const res = getModesRecForId(id)
      arrPromise.push(res as unknown as Promise<Promises>)
    }

    void Promise.allSettled(arrPromise)
      .then(([paramsMixing, paramsRecord]) => {
        const values = {
          record: paramsRecord?.status === 'fulfilled' ? (paramsRecord?.value?.record ?? false) : false,
          mixing: paramsMixing.status === 'fulfilled' ? (paramsMixing?.value?.mixing ?? false) : false,
        }
        setValue(values)
        setOriginalValues(values)
      })
      .catch((e) => {
        console.log(e)
      })
  }, [])

  const handleChange = (key: string, value: boolean) => {
    const newValues = {
      ...values,
      [key]: value,
    }
    setValue(newValues)

    setIsVisibleActionBtns(
      !areObjectsEqual(newValues as unknown as AnyObject, originalValues as unknown as AnyObject, false),
    )
  }

  useHotkeys('ctrl+shift+s', () => isVisibleActionBtns && onConfirm && object.value && onConfirm(object.value, values))
  useHotkeys('ctrl+shift+x', () => isVisibleActionBtns && onClose && onClose())

  return (
    <Modal
      title='Настройки станции'
      maxWidth='sm'
      onClose={onClose}
      className={classNames(cls.SettingsModal, {}, className ? [className] : [])}
      actions={
        <div className={cls.Actions}>
          {isVisibleActionBtns && (
            <>
              <Button variant='outlined' onClick={onClose}>
                Сбросить
              </Button>
              <Button onClick={() => onConfirm && object.value && onConfirm(object.value, values)}>Сохранить</Button>
            </>
          )}
        </div>
      }
    >
      <div className={classNames(cls.Body, {}, [])}>{object?.label}</div>
      <>
        {!object.viewOnly && (
          <Switch
            checked={values.record}
            onChange={(_, checked: boolean) => {
              handleChange('record', checked)
            }}
            label='Запись в МОДЕС'
          />
        )}
        <Switch
          checked={values.mixing}
          onChange={(_, checked: boolean) => {
            handleChange('mixing', checked)
          }}
          label='Свод'
        />
      </>
    </Modal>
  )
})

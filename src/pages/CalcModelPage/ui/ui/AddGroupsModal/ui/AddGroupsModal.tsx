import WarningIcon from '@mui/icons-material/Warning'
import { Dispatch, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { classNames } from 'shared/lib/classNames/classNames'
import { generateUUID } from 'shared/lib/GenerationUUID'
import { But<PERSON> } from 'shared/ui/Button'
import { LabeledCheckbox } from 'shared/ui/LabeledCheckbox'
import { Modal } from 'shared/ui/Modal'
import { TextField } from 'shared/ui/TextField'

import cls from './AddGroupsModal.module.scss'

export interface IObj {
  initialSelectArr: IObj[]
  initialSelect: string[]
  name: string
  valueUuid: string
  type: string
  id: string
  startDate: string
}

interface AddGroupsModalProps {
  className?: string
  object?: IObj
  setObject?: Dispatch<IObj | object>
  onClose?: () => void
  ggs?: IObj[]
  onConfirm?: (res: { type: string; value: string }) => void
}

export const AddGroupsModal = (props: AddGroupsModalProps) => {
  const { className, object, setObject, onClose, ggs, onConfirm } = props
  const [search, setSearch] = useState('')
  const finalGGS = [
    ...(object?.initialSelectArr ?? []),
    ...(ggs ?? []).filter((el) => {
      return el.name.toUpperCase().includes(search.toUpperCase())
    }),
  ]

  const [select, setSelect] = useState(object?.initialSelect ?? [])

  const isDisabled = select?.length > 1 && object && object?.name && object?.name?.trim()?.length > 0

  const handleSaveChanges = () => {
    const res = {
      type: object?.valueUuid ? 'edit' : 'create',
      name: object?.name ?? '',
      value: object?.valueUuid ?? generateUUID(),
      ids: select,
      childs: select
        .map((el) => {
          const find = finalGGS.find((item) => item.id === el)
          if (find) {
            return {
              id: find.id,
              name: find.name,
              startDate: find.startDate,
              value: find.id,
            }
          } else {
            return null
          }
        })
        .filter((el) => el !== null),
    }
    onConfirm && onConfirm(res)
  }

  const handleResetChanges = () => {
    setSelect([])
    setObject && setObject({})
  }

  useHotkeys('ctrl+shift+s', () => isDisabled && handleSaveChanges())
  useHotkeys('ctrl+shift+x', () => isDisabled && handleResetChanges())

  return (
    <Modal
      title={`${object?.type === 'edit' ? 'Редактирование' : 'Создание'} группы`}
      onClose={onClose}
      className={classNames(cls.AddGroupsModal, {}, className ? [className] : [])}
      actions={
        isDisabled ? (
          <div className={classNames(cls.Actions, {}, [])}>
            <Button className={classNames(cls.Action, {}, [])} variant='outlined' onClick={handleResetChanges}>
              Сбросить
            </Button>
            <Button className={classNames(cls.Action, {}, [])} onClick={handleSaveChanges}>
              Сохранить
            </Button>
          </div>
        ) : (
          <div className={classNames(cls.Actions, { [cls.ErrorMessage]: true }, [])}>
            <WarningIcon />
            Введите название группы и выберите более одного ГГ
          </div>
        )
      }
    >
      <div>
        <div className={classNames(cls.Description, {}, [])}>Учёт связанной работы ГГ</div>
        <div className={classNames(cls.CreateRow, {}, [])}>
          <div>Название группы</div>
          <TextField
            value={object?.name ?? ' '}
            onChange={(e) =>
              setObject &&
              setObject((prev: IObj) => ({
                ...prev,
                name: e.target.value,
              }))
            }
            className={classNames(cls.InputName, {}, [])}
          />
        </div>
        <div className={classNames(cls.SearchRow, {}, [])}>
          <TextField
            className={classNames(cls.InputSearch, {}, [])}
            variant='standard'
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            placeholder='Поиск'
          />
        </div>
        <div className={classNames(cls.GGContainer, {}, [])}>
          {finalGGS.map((el) => {
            const isFind = select.some((item) => el.id === item)

            return (
              <LabeledCheckbox
                checked={isFind}
                label={el.name}
                className={cls.GG}
                onChange={() =>
                  setSelect((prev) => {
                    if (isFind) {
                      return prev.filter((item) => item !== el.id)
                    }

                    return [...prev, el.id]
                  })
                }
              />
            )
          })}
        </div>
      </div>
    </Modal>
  )
}

.AddGroupsModal {
}

.Actions {
  height: 42px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 4px;
}

.ErrorMessage {
  color: var(--orange-color);
  margin-right: 10px;
}

.Action {
  margin: 0 10px;
}

.Description {
  color: rgb(130 130 130 / 85%);
  font-size: 18px;
}

.CreateRow {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.InputName {
  width: 240px;
  margin: 0 10px;
  input {
    height: 14px;
    padding: 4px 12px !important;
  }
}

.SearchRow {
  width: 100%;
  height: 34px;
  margin: 10px 0;
}

.InputSearch {
  width: 100%;

  input {
    width: 100%;
    height: 24px;
    padding: 4px 12px !important;
  }
}

.GGContainer {
  //border: solid 1px red;
  width: 100%;
  height: 300px;
  overflow: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.GG {
  width: fit-content;
}

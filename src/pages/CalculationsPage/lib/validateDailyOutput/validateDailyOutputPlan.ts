import Handsontable from 'handsontable'
import { isOneOfValueInColumnNotEqualZero } from 'pages/CalculationsPage/lib/validateDailyOutput/helpers.ts'
import { IVaultStore } from 'stores/CalculationsPageStore/VaultStore'

import { IValidateSumIdx } from '.'

export const validateDailyOutputPlan = (
  hot: Handsontable,
  inputValues: Omit<IVaultStore['inputValues'][0], 'plantId'>,
  sumIdx: IValidateSumIdx & { pGen: number },
  active: boolean,
  rguLength: number,
) => {
  const tableDataRow = hot.getDataAtRow(24)
  const P_MIN_SUM = tableDataRow[sumIdx.pMin]
  const P_MAX_SUM = tableDataRow[sumIdx.pMax]
  const P_GEN_SUM = tableDataRow[sumIdx.pGen]

  const P_GEN_TARGET = inputValues?.P_GEN_TARGET
    ? String(inputValues?.P_GEN_TARGET)
        ?.split('')
        ?.map((el) => {
          if (el === ',') {
            return '.'
          }

          return el
        })
        .join('')
    : 0

  const value = Number(P_GEN_TARGET)
  let res: string[] = []
  const isNumberEntered = !Number.isNaN(parseFloat(String(inputValues?.P_GEN_TARGET)))

  if (isNumberEntered) {
    if (value > P_MAX_SUM) {
      res = [...res, `Значение больше суммы Итог(макс)`]
    }
    if (value < P_MIN_SUM) {
      res = [...res, `Значение меньше суммы Итог(мин)`]
    }
    if (Number(Math.abs(value - P_GEN_SUM).toFixed(3)) > 0.01) {
      res = [...res, `Значение отличается от ΣИтог(план) более чем на 0,01 млн.кВтч`]
    }
    if (Number(inputValues.W_MIN) < 0) {
      res = [...res, 'Эплан должно быть положительным']
    }

    const isOneOfPlanNotEqualZero = isOneOfValueInColumnNotEqualZero(hot, sumIdx.pGen)

    /**
     * sumIdx.pMax - индекс столбца Итог.Макс
     * rguLength * 3 - кол-во столбцов, которые занимают РГЕ, в колонке Итог
     * 2 - после колонки Итог, идет колонка Резервы с вложенными колонками: Rмакс и АВРЧМ (целевая)
     * */
    const isOneOfResultAvrchmNotEqualZero = isOneOfValueInColumnNotEqualZero(hot, sumIdx.pMax + rguLength * 3 + 2)

    if (value === 0 && (isOneOfPlanNotEqualZero || isOneOfResultAvrchmNotEqualZero)) {
      res = [...res, 'Если Эплан = 0, то все значения Итог(план) и АВРЧМ должны быть равны 0']
    }
  }

  if (active && !isNumberEntered) {
    res = [...res, 'Эплан должно быть заполнено']
  }

  if (res.length > 0) {
    return res.join('\n')
  } else {
    return undefined
  }
}

import Handsontable from 'handsontable'
import { isOneOfValueInColumnNotEqualZero } from 'pages/CalculationsPage/lib/validateDailyOutput/helpers.ts'
import { IVaultStore } from 'stores/CalculationsPageStore/VaultStore'

import { IValidateSumIdx } from '.'

export const validateDailyOutputMin = (
  hot: Handsontable,
  inputValues: Omit<IVaultStore['inputValues'][0], 'plantId'>,
  sumIdx: IValidateSumIdx & { pGen: number },
  active: boolean,
  rguLength: number,
) => {
  const tableDataRow = hot.getDataAtRow(24)
  const P_MIN_SUM = tableDataRow[sumIdx.pMin]

  const W_MIN = inputValues?.W_MIN
    ? String(inputValues?.W_MIN)
        ?.split('')
        ?.map((el) => {
          if (el === ',') {
            return '.'
          }

          return el
        })
        .join('')
    : 0

  const value = Number(W_MIN)
  const W_MAX = inputValues?.W_MAX
    ? String(inputValues?.W_MAX)
        ?.split('')
        ?.map((el) => {
          if (el === ',') {
            return '.'
          }

          return el
        })
        .join('')
    : 0

  let res: string[] = []
  const isWMax = !Number.isNaN(parseFloat(String(inputValues?.W_MAX)))
  const isWMin = !Number.isNaN(parseFloat(String(inputValues?.W_MIN)))

  if (isWMax && isWMin && !(value === 0 && Number(W_MAX) === 0) && value >= Number(W_MAX)) {
    res = [...res, 'Значение больше или равно Эмакс']
  }

  if (isWMin && value < P_MIN_SUM) {
    res = [...res, 'Значение меньше суммы Итог(мин)']
  }

  if (active && !isWMin) {
    res = [...res, 'Эмин должно быть заполнено']
  } else if (Number(inputValues.W_MIN) < 0) {
    res = [...res, 'Эмин должно быть положительным']
  }

  const isOneOfResultMinNotEqualZero = isOneOfValueInColumnNotEqualZero(hot, sumIdx.pMin)

  const isOneOfResultMaxNotEqualZero = isOneOfValueInColumnNotEqualZero(hot, sumIdx.pMax)

  /**
   * sumIdx.pMax - индекс столбца Итог.Макс
   * rguLength * 3 - кол-во столбцов, которые занимают РГЕ, в колонке Итог
   * 2 - после колонки Итог, идет колонка Резервы с вложенными колонками: Rмакс и АВРЧМ (целевая)
   * */
  const isOneOfResultAvrchmNotEqualZero = isOneOfValueInColumnNotEqualZero(hot, sumIdx.pMax + rguLength * 3 + 2)

  if (
    isWMin &&
    value === 0 &&
    (Number(W_MAX) !== 0 ||
      isOneOfResultMinNotEqualZero ||
      isOneOfResultMaxNotEqualZero ||
      isOneOfResultAvrchmNotEqualZero)
  ) {
    res = [...res, 'Если Эмин = 0, то Эмакс и все значения Итог(макс), Итог(мин) и АВРЧМ должны быть равны 0']
  }

  if (res.length > 0) {
    return res.join('\n')
  } else {
    return undefined
  }
}

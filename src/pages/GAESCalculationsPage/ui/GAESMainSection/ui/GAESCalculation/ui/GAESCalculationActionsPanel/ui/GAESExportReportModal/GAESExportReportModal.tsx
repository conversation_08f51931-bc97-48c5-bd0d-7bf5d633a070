import { Tooltip } from '@mui/material'
import { addDays, format } from 'date-fns'
import { IGAESXlsReportParams } from 'entities/api/gaesCalculationsManager.entities'
import { observer } from 'mobx-react'
import { Dispatch, SetStateAction, useState } from 'react'
import { formatDate } from 'shared/lib/dateFormates'
import { LabeledCheckbox } from 'shared/ui/LabeledCheckbox'
import { LoadingButton } from 'shared/ui/LoadingButton'
import { Modal } from 'shared/ui/Modal'
import { Row } from 'shared/ui/Row'
import { TextField } from 'shared/ui/TextField'
import { useStore } from 'stores/useStore'

import cls from './GAESExportReportModal.module.scss'

interface GAESExportReportModalProps {
  onClose: () => void
}

export const GAESExportReportModal = observer((props: GAESExportReportModalProps) => {
  const { onClose } = props
  const { gaesCalculationsStore } = useStore()
  const { gaesCalculationTabStore, initialDate, selectedPlant } = gaesCalculationsStore
  const { isXlsExportInProgress, getCalculationXlsReport } = gaesCalculationTabStore

  // Генерируем даты в формате yyyy-MM-dd для отправки на сервер
  const previousDateISO = format(addDays(initialDate, -1), 'yyyy-MM-dd')
  const datesISO = Array.from({ length: 4 }, (_, i) => format(addDays(initialDate, i), 'yyyy-MM-dd'))

  const [selectedDates, setSelectedDates] = useState<string[]>(datesISO)
  const [prevDates, setPrevDates] = useState<string[]>([previousDateISO])
  const [selectedColumns, setSelectedColumns] = useState<IGAESXlsReportParams['columns']>([
    'P_GEN',
    'MODES_P_MIN',
    'MODES_P_MAX',
    'CONSUMPT',
  ])

  const checkItem = <T,>(item: NoInfer<T>, setList: Dispatch<SetStateAction<T[]>>) => {
    setList((prevList) =>
      prevList.includes(item) ? prevList.filter((listItem) => listItem !== item) : [...prevList, item],
    )
  }

  const handleDownload = async () => {
    if (!selectedPlant) return

    const params: IGAESXlsReportParams = {
      plantId: selectedPlant.plantId,
      dates: selectedDates,
      prevDates: prevDates.length > 0 ? prevDates : undefined,
      columns: selectedColumns,
    }

    try {
      await getCalculationXlsReport(params, selectedPlant.name)
      onClose()
    } catch (error) {
      console.error('Ошибка при выгрузке отчета:', error)
    }
  }

  // Условие для блокировки кнопки и сообщения в Tooltip
  const isDateMissing = selectedDates.length === 0
  const isColumnMissing = selectedColumns.length === 0
  const isButtonDisabled = isDateMissing || isColumnMissing

  const disabledMessage =
    isDateMissing && isColumnMissing
      ? 'Выберите хотя бы одну дату и одну колонку'
      : isDateMissing
        ? 'Выберите хотя бы одну дату'
        : isColumnMissing
          ? 'Выберите хотя бы одну колонку'
          : ''

  return (
    <Modal
      maxWidth='xl'
      title='Выгрузка отчёта по станции'
      onClose={onClose}
      actions={
        <div className={cls.footer}>
          <Tooltip title={isButtonDisabled ? disabledMessage : ''}>
            <span>
              <LoadingButton
                variant='contained'
                loading={isXlsExportInProgress}
                disabled={isButtonDisabled}
                onClick={handleDownload}
                className={cls.downloadButton}
              >
                Выгрузить
              </LoadingButton>
            </span>
          </Tooltip>
        </div>
      }
    >
      <div className={cls.body}>
        <Row label='Станция'>
          <TextField disabled className={cls.plantTitle} value={selectedPlant?.name} />
        </Row>
        <Row label='Предыдущая дата' contentClassName={cls.dateCheckSection}>
          <LabeledCheckbox
            label=''
            checked={prevDates.includes(previousDateISO)}
            onChange={() => checkItem(previousDateISO, setPrevDates)}
          />
          <TextField disabled className={cls.date} value={formatDate(previousDateISO)} />
        </Row>
        {datesISO.map((dateISO, index) => (
          <Row
            key={dateISO}
            label={index === 1 ? 'Даты' : null}
            contentClassName={cls.dateCheckSection}
            labelClassName={cls.datesLabel}
          >
            <LabeledCheckbox
              label=''
              checked={selectedDates.includes(dateISO)}
              onChange={() => checkItem(dateISO, setSelectedDates)}
            />
            <TextField disabled className={cls.date} value={formatDate(dateISO)} />
          </Row>
        ))}
        <Row label='Итог'>
          <LabeledCheckbox
            label='план'
            checked={selectedColumns.includes('P_GEN')}
            onChange={() => checkItem('P_GEN', setSelectedColumns)}
          />
        </Row>
        <Row label='Модес' contentClassName={cls.modesCheckSection}>
          <LabeledCheckbox
            label='мин'
            checked={selectedColumns.includes('MODES_P_MIN')}
            onChange={() => checkItem('MODES_P_MIN', setSelectedColumns)}
          />
          <LabeledCheckbox
            label='макс'
            checked={selectedColumns.includes('MODES_P_MAX')}
            onChange={() => checkItem('MODES_P_MAX', setSelectedColumns)}
          />
        </Row>
        <Row label='ИСП'>
          <LabeledCheckbox
            label='потр'
            checked={selectedColumns.includes('CONSUMPT')}
            onChange={() => checkItem('CONSUMPT', setSelectedColumns)}
          />
        </Row>
      </div>
    </Modal>
  )
})

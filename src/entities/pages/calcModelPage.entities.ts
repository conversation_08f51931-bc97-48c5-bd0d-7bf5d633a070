import { IAddConsumptionSchedulesInput } from 'entities/api/calcModelPage.entities.ts'
import { IUserDetails } from 'entities/store/auth.entities.ts'
import { CalcModelAvrchmSettingsSource } from 'entities/store/calcModelStore.entities.ts'

export interface ModalAddProps {
  className?: string | undefined
  onClose: () => void
  onSave?: () => void
}

export interface FlatDataProps {
  tabId: string
  id: string
  name: string
}

export interface ListOfConsumptionSchedulesProps {
  className?: string
  userDetail: IUserDetails
}

export interface IRowListOfConsumptionSchedules {
  energyDistrictIspId: number
  id: number
  notation: string
  canDelete: boolean
}

export interface IObjectAddAndEdit {
  type: 'add' | 'edit'
  energyDistrictIspId?: number
  id?: number
  notation?: string
}

export interface IResObj
  extends Omit<Omit<IAddConsumptionSchedulesInput, 'consumptionFormulaList'>, 'energyDistrictId'> {
  energyDistrictId?: string
  consumptionFormulaList?: {
    planingStage?: string
    planingStageIsp?: string
    departmentLevel?: string
  }[]
}

export interface AddConsumptionSchedulesProps {
  className?: string
  onClose?: () => void
  object: IObjectAddAndEdit
  onConfirm?: (_: IResObj) => void
}
export interface IRowAddAndEditConsumptionSchedules {
  dcLevel: string
  tabId: string
  titleICP: string
  tempEdits: string[]
  isEdit: boolean
  code: number
  value: number
  formula: string
}

export interface ITerr {
  value: number
  ispId: number
  energyDistrictIspId: number
}

export interface ISourceItem {
  value: CalcModelAvrchmSettingsSource
  label: string
}

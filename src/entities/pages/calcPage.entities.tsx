import CloseRoundedIcon from '@mui/icons-material/CloseRounded'
import DoneIcon from '@mui/icons-material/Done'
import PriorityHighIcon from '@mui/icons-material/PriorityHigh'
import { Tooltip } from '@mui/material'
import { IGetCalculationXlsInput, TStatusUpdateModalVault } from 'entities/api/calculationsManager.entities'
import { PlanningStage } from 'entities/shared/common.entities.ts'
import { ICalcModelPlantListItem } from 'entities/store/calcModelStore.entities.ts'
import cls from 'pages/CalculationsPage/ui/VaultBody/ui/ModalUpdateVault/ui/ModalUpdateVault.module.scss'
import { ReactNode } from 'react'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { formatDateTime } from 'shared/lib/dateFormates'
import { locationParse } from 'shared/lib/locationParse'
import { Icon } from 'shared/ui/Icon'

export interface IStage {
  value: PlanningStage
  label: string
  color: string
  finished: boolean
}

export interface ILeftDataMenu {
  plantId: number
}

export interface IExportModalGesChildren {
  label: string
  value: IGetCalculationXlsInput['columns'][0]
  checked: boolean
}

export interface IExportModalGesColumn {
  label: string
  children: IExportModalGesChildren[]
}

export interface ExportModalProps {
  onClose: VoidFunction
  selectedPlant: ICalcModelPlantListItem
  planningStage: IStage['value']
  plantType: 'GES' | 'GAES'
  regulatedUnit: string
  disabled?: boolean
}

const { year = null, month = null, day = null } = locationParse(location.search)
const nowDate = new Date()
nowDate.setDate(nowDate.getDate() + 1)
const initDateLocal = year && month && day ? new Date(`${year}-${month}-${day}`) : nowDate

export const getPrepareDate = (initDate: Date | void) => {
  const date = initDate ?? initDateLocal
  const year = date.getFullYear()
  const month = date.getMonth() + 1 > 9 ? date.getMonth() + 1 : `0${date.getMonth() + 1}`
  const day = date.getDate() > 9 ? date.getDate() : `0${date.getDate()}`

  return `${year}-${month}-${day}`
}
//ModalUpdateVault
export const SIZE_ICON = 16

export interface IStatusModalUpdateVault {
  type?: TStatusUpdateModalVault
  tooltip: ReactNode
}

export interface IValueForTableModalUpdateValue {
  status: TStatusUpdateModalVault
  warnings?: string[]
  userFio?: string
  updatedDate?: string
}

export const getStatusName = (status: TStatusUpdateModalVault) => {
  switch (status) {
    case 'IN_PROCESS':
      return `Действие в процессе выполнения`
    case 'FAILED':
      return `Действие не выполнено (ошибка)`
    case 'NOT_EXECUTED':
      return `Действие не запускалось`
    case 'WARNING':
      return `Действие выполнено с предупреждением`
    case 'DONE':
      return `Действие успешно завершено`
  }
}

export const columnBands = [
  {
    title: 'Загрузка исходных данных',
    children: [
      { columnName: 'LOAD_CONSUMPTION' },
      { columnName: 'LOAD_GENERATOR_ALLOWED_ZONES' },
      { columnName: 'LOAD_PLANT_DATA' },
      { columnName: 'LOAD_CM_DATA' },
    ],
  },
  {
    title: 'Расчёты',
    children: [
      { columnName: 'CALC_ALLOWED_ZONES' },
      { columnName: 'CALC_GENERATION' },
      { columnName: 'OPTIMIZATION' },
      { columnName: 'CALC_ENTERING_ALLOWED_ZONES' },
      { columnName: 'CALC_ENTERING_ALLOWED_ZONES_TO_BOUNDS' },
      { columnName: 'CALC_BALANCE_RGU' },
    ],
  },
]

export const getMessageTooltip = (
  status: TStatusUpdateModalVault,
  userFio?: string,
  updatedDate?: string,
  warnings?: string[],
) => {
  return (
    <div className={cls.tooltipContainer}>
      <div>{getStatusName(status)}</div>
      {userFio && <div>Инициатор: {userFio}</div>}
      {updatedDate && <div>Дата, время: {formatDateTime(updatedDate)}</div>}
      {warnings && warnings?.length > 0 && (
        <div>
          <div>Список предупреждений:</div>
          {warnings.map((warning) => {
            return <div>{warning}</div>
          })}
        </div>
      )}
    </div>
  )
}

export const Status = ({ type, tooltip }: IStatusModalUpdateVault) => {
  switch (type) {
    case 'IN_PROCESS':
      return (
        <div className={cls.IconContainer}>
          <Tooltip title={tooltip}>
            <div className={classNames(cls.Icon, {}, [cls.IN_PROCESS])}>
              <Icon name='update' width={SIZE_ICON} height={SIZE_ICON} />
            </div>
          </Tooltip>
        </div>
      )
    case 'FAILED':
      return (
        <div className={cls.IconContainer}>
          <Tooltip title={tooltip}>
            <div className={classNames(cls.Icon, {}, [cls.FAILED])}>
              <CloseRoundedIcon width={SIZE_ICON} height={SIZE_ICON} />
            </div>
          </Tooltip>
        </div>
      )
    case 'NOT_EXECUTED':
      return (
        <div className={cls.IconContainer}>
          <Tooltip title={tooltip}>
            <div className={classNames(cls.Icon, {}, [cls.NOT_EXECUTED])}></div>
          </Tooltip>
        </div>
      )
    case 'WARNING':
      return (
        <div className={cls.IconContainer}>
          <Tooltip classes={{ popper: cls.TooltipContainer }} title={tooltip}>
            <div className={classNames(cls.Icon, {}, [cls.WARNING])}>
              <PriorityHighIcon width={SIZE_ICON} height={SIZE_ICON} />
            </div>
          </Tooltip>
        </div>
      )
    case 'DONE':
      return (
        <div className={cls.IconContainer}>
          <Tooltip title={tooltip}>
            <div className={classNames(cls.Icon, {}, [cls.DONE])}>
              <DoneIcon width={SIZE_ICON} height={SIZE_ICON} />
            </div>
          </Tooltip>
        </div>
      )
    default:
      return <></>
  }
}

export const columns = [
  {
    name: 'plantName',
    title: 'Станция',
    width: 200,
  },
  {
    name: 'LOAD_CONSUMPTION',
    title: 'ИСП',
    width: 65,
    render: (value: IValueForTableModalUpdateValue) => {
      return (
        <Status
          type={value.status}
          tooltip={getMessageTooltip(value.status, value?.userFio, value?.updatedDate, value?.warnings)}
        />
      )
    },
  },
  {
    name: 'LOAD_GENERATOR_ALLOWED_ZONES',
    title: 'Модес (зз)',
    width: 90,
    render: (value: IValueForTableModalUpdateValue) => {
      return (
        <Status
          type={value.status}
          tooltip={getMessageTooltip(value.status, value?.userFio, value?.updatedDate, value?.warnings)}
        />
      )
    },
  },
  {
    name: 'LOAD_PLANT_DATA',
    title: 'Модес (станции, РГЕ)',
    width: 160,
    render: (value: IValueForTableModalUpdateValue) => {
      return (
        <Status
          type={value.status}
          tooltip={getMessageTooltip(value.status, value?.userFio, value?.updatedDate, value?.warnings)}
        />
      )
    },
  },
  {
    name: 'LOAD_CM_DATA',
    title: 'РМ',
    width: 65,
    render: (value: IValueForTableModalUpdateValue) => {
      return (
        <Status
          type={value.status}
          tooltip={getMessageTooltip(value.status, value?.userFio, value?.updatedDate, value?.warnings)}
        />
      )
    },
  },
  {
    name: 'CALC_ALLOWED_ZONES',
    title: 'Допустимые зоны',
    width: 145,
    render: (value: IValueForTableModalUpdateValue) => {
      return (
        <Status
          type={value.status}
          tooltip={getMessageTooltip(value.status, value?.userFio, value?.updatedDate, value?.warnings)}
        />
      )
    },
  },
  {
    name: 'CALC_GENERATION',
    title: 'Плановый график',
    width: 145,
    render: (value: IValueForTableModalUpdateValue) => {
      return (
        <Status
          type={value.status}
          tooltip={getMessageTooltip(value.status, value?.userFio, value?.updatedDate, value?.warnings)}
        />
      )
    },
  },
  {
    name: 'OPTIMIZATION',
    title: 'Оптимизация',
    width: 120,
    render: (value: IValueForTableModalUpdateValue) => {
      return (
        <Status
          type={value.status}
          tooltip={getMessageTooltip(value.status, value?.userFio, value?.updatedDate, value?.warnings)}
        />
      )
    },
  },
  {
    name: 'CALC_ENTERING_ALLOWED_ZONES',
    title: 'Ввод в доп.область 1',
    width: 160,
    render: (value: IValueForTableModalUpdateValue) => {
      return (
        <Status
          type={value.status}
          tooltip={getMessageTooltip(value.status, value?.userFio, value?.updatedDate, value?.warnings)}
        />
      )
    },
  },
  {
    name: 'CALC_ENTERING_ALLOWED_ZONES_TO_BOUNDS',
    title: 'Ввод в доп.область 2',
    width: 160,
    render: (value: IValueForTableModalUpdateValue) => {
      return (
        <Status
          type={value.status}
          tooltip={getMessageTooltip(value.status, value?.userFio, value?.updatedDate, value?.warnings)}
        />
      )
    },
  },
  {
    name: 'CALC_BALANCE_RGU',
    title: 'Распределение по РГЕ',
    width: 170,
    render: (value: IValueForTableModalUpdateValue) => {
      return (
        <Status
          type={value.status}
          tooltip={getMessageTooltip(value.status, value?.userFio, value?.updatedDate, value?.warnings)}
        />
      )
    },
  },
]

export type UpdateVaultColumnsCode =
  | 'LOAD_CONSUMPTION'
  | 'LOAD_GENERATOR_ALLOWED_ZONES'
  | 'LOAD_PLANT_DATA'
  | 'LOAD_CM_DATA'
  | 'CALC_ALLOWED_ZONES'
  | 'CALC_GENERATION'
  | 'OPTIMIZATION'
  | 'CALC_ENTERING_ALLOWED_ZONES'
  | 'CALC_ENTERING_ALLOWED_ZONES_TO_BOUNDS'
  | 'CALC_BALANCE_RGU'

export const columnSearchDisabled: UpdateVaultColumnsCode[] = [
  'LOAD_CONSUMPTION',
  'LOAD_GENERATOR_ALLOWED_ZONES',
  'LOAD_PLANT_DATA',
  'LOAD_CM_DATA',
  'CALC_ALLOWED_ZONES',
  'CALC_GENERATION',
  'OPTIMIZATION',
  'CALC_ENTERING_ALLOWED_ZONES',
  'CALC_ENTERING_ALLOWED_ZONES_TO_BOUNDS',
  'CALC_BALANCE_RGU',
]

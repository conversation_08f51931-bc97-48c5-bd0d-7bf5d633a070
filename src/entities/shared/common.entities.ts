export enum PlaningStageCategory {
  VSVGO = 'VSVGO',
  RSV = 'RSV',
  PBR = 'PBR',
}

export enum PlanningStage {
  VSVGO1 = 'VSVGO1',
  VSVGO2 = 'VSVGO2',
  VSVGO3 = 'VSVGO3',
  PER = 'PER',
  PDG = 'PDG',
}

export enum PlanningStageRussia {
  VSVGO1 = 'ВСВГО-1',
  VSVGO2 = 'ВСВГО-2',
  VSVGO3 = 'ВСВГО-3',
  PER = 'ПЭР',
  PDG = 'ПДГ',
}

export enum CalculationTaskType {
  LOAD_CONSUMPTION = 'LOAD_CONSUMPTION',
  LOAD_PLANT_DATA = 'LOAD_PLANT_DATA',
  LOAD_CM_DATA = 'LOAD_CM_DATA',
  LOAD_GENERATOR_ALLOWED_ZONES = 'LOAD_GENERATOR_ALLOWED_ZONES',
  OPTIMIZATION = 'OPTIMIZATION',
  WRITE_PLAN_RGU_DATA = 'WRITE_PLAN_RGU_DATA',
  CALC_ALLOWED_ZONES = 'CALC_ALLOWED_ZONES',
  CALC_GENERATION = 'CALC_GENERATION',
  CALC_ENTERING_ALLOWED_ZONES = 'CALC_ENTERING_ALLOWED_ZONES',
  CALC_ENTERING_ALLOWED_ZONES_TO_BOUNDS = 'CALC_ENTERING_ALLOWED_ZONES_TO_BOUNDS',
  CALC_BALANCE_RGU = 'CALC_BALANCE_RGU',
  LOAD_AVRCHM = 'LOAD_AVRCHM',
}

/**
 * Статусы выполнения асинхронной задачи
 */
export enum TaskStatus {
  // задача в процессе
  IN_PROCESS = 'IN_PROCESS',
  // задача выполнена успешн
  DONE = 'DONE',
  // задача выполнена с ошибкой
  FAILED = 'FAILED',
  // задача не выполнялась
  NOT_EXECUTED = 'NOT_EXECUTED',
}

export enum CalculationColumn {
  /** Итог мин **/
  RESULT_MIN = 'RESULT_MIN',
  /** Итог план **/
  P_GEN = 'P_GEN',
  /** Итог макс **/
  RESULT_MAX = 'RESULT_MAX',
  /** Итог исх **/
  P_SOURCE = 'P_SOURCE',
  /** Резервы Rмакс **/
  RESERVES_MAX = 'RESERVES_MAX',
  /** Резервы АВРЧМ **/
  AVRCHM_LOAD = 'AVRCHM_LOAD',
  /** Резервы АВРЧМ ↓ **/
  AVRCHM_UNLOAD = 'AVRCHM_UNLOAD',
  /** Резервы НПРЧ **/
  NPRCH = 'NPRCH',
  /** Итог.ОГР мин **/
  LIMIT_MIN = 'LIMIT_MIN',
  /** Итог.ОГР макс **/
  LIMIT_MAX = 'LIMIT_MAX',
  /** РМ мин **/
  CM_P_MIN = 'CM_P_MIN',
  /** РМ макс **/
  CM_P_MAX = 'CM_P_MAX',
  /** Модес мин **/
  MODES_P_MIN = 'MODES_P_MIN',
  /** Модес макс **/
  MODES_P_MAX = 'MODES_P_MAX',
  /** Модес заяв **/
  MODES_DECLARED = 'MODES_DECLARED',
  /** ИСП потр **/
  CONSUMPT = 'CONSUMPT',
}

export enum TypedParameter {
  BOOLEAN = 'BOOLEAN',
  TOGGLEABLE = 'TOGGLEABLE',
  INTEGER = 'INTEGER',
  TOGGLEABLE_INTEGER = 'TOGGLEABLE_INTEGER',
  REGISTRY_TYPE = 'REGISTRY_TYPE',
  BIGDECIMAL = 'BIGDECIMAL',
  TOGGLEABLE_BIGDECIMAL = 'TOGGLEABLE_BIGDECIMAL',
  TOGGLEABLE_GENERATOR_GROUPS = 'TOGGLEABLE_GENERATOR_GROUPS',
  TOGGLEABLE_PRESSURE_RECESSION = 'TOGGLEABLE_PRESSURE_RECESSION',
  TOGGLEABLE_RESTRICTION_LIMIT = 'TOGGLEABLE_RESTRICTION_LIMIT',
  TOGGLEABLE_ENERGY_DISTRICT = 'TOGGLEABLE_ENERGY_DISTRICT',
  TOGGLEABLE_LOAD_UNLOAD_SPEED = 'TOGGLEABLE_LOAD_UNLOAD_SPEED',
  LOCAL_DATE = 'LOCAL_DATE',
}

export enum CalculationInput {
  /** Эплан */
  P_GEN_TARGET = 'P_GEN_TARGET',
  /** Эмин */
  W_MIN = 'W_MIN',
  /** Эмакс */
  W_MAX = 'W_MAX',
  /** Учёт режима половодья */
  FLOOD_MODE_WATCH = 'FLOOD_MODE_WATCH',
  /** Дата, по которой загружаются данные из ИСП */
  ISP_DATE = 'ISP_DATE',
  /** Третичный резерв */
  TERTIARY_RESERVE = 'TERTIARY_RESERVE',
}

export interface IRguGenerationInput {
  targetDate: string
  planingStage: PlanningStage
  plantId: number
}

export enum PlantType {
  /** ГЭС (Гидроэлектростанция) */
  GES = 'GES',
  /** ГАЭС (Гидроаккумулирующая электростанция) */
  GAES = 'GAES',
}

export enum RegistryType {
  /** Диспетчерский центр */
  DEPARTMENT = 'DEPARTMENT',
  /** Станция */
  PLANT = 'PLANT',
  /** Гидрогенератор */
  GENERATOR = 'GENERATOR',
  /** Режимная генерирующая единица */
  RGU = 'RGU',
  /** Связь ГГ - РГЕ */
  RGU_RELATION = 'RGU_RELATION',
}

export enum PlantParameter {
  /** Регулируемая единица */
  REGULATED_UNIT = 'REGULATED_UNIT',
  /** Оптимизация */
  OPTIMIZATION = 'OPTIMIZATION',
  /** КПД ГАЭС */
  EFFICIENCY = 'EFFICIENCY',
  /** Связь с графиком потребления */
  CONSUMPTION_SCHEDULE_BINDING = 'CONSUMPTION_SCHEDULE_BINDING',
  /** Участие в НПРЧ */
  PARTICIPATION_NPRCH = 'PARTICIPATION_NPRCH',
  /** Участие в АВРЧМ */
  PARTICIPATION_AVRCHM = 'PARTICIPATION_AVRCHM',
  /** Учёт связанной работы ГГ */
  GENERATOR_JOINT_WORK_WATCH = 'GENERATOR_JOINT_WORK_WATCH',
  /** Ограничение минимума */
  MINIMUM_LIMIT = 'MINIMUM_LIMIT',
  /** Ограничение максимума */
  MAXIMUM_LIMIT = 'MAXIMUM_LIMIT',
  /** Учёт снижения напора */
  PRESSURE_RECESSION_WATCH = 'PRESSURE_RECESSION_WATCH',
  /** Группа РГЕ */
  RGU_GROUP = 'RGU_GROUP',
  /** Δ(Эмакс-Эмин) */
  E_MAX_E_MIN = 'E_MAX_E_MIN',
  /** Допустимая скорость изменения нагрузки между часами */
  LOAD_UNLOAD_SPEED = 'LOAD_UNLOAD_SPEED',
  /** Учёт режима половодья */
  FLOOD_MODE_WATCH = 'FLOOD_MODE_WATCH',
  /** Третичныйй резерв */
  TERTIARY_RESERVE = 'TERTIARY_RESERVE',
}

export enum SummaryColumn {
  /** ТЭС при АВРЧМ на загрузку */
  TES_AVRCHM_LOAD = 'TES_AVRCHM_LOAD',
  /** ТЭС при АВРЧМ на разгрузку */
  TES_AVRCHM_UNLOAD = 'TES_AVRCHM_UNLOAD',
  /** АВРЧМ станции из Модес */
  MODES_AVRCHM = 'MODES_AVRCHM',
}

export interface Protocol {
  result?: string
  warnings: string[]
  errors: string[]
}

export interface IBigDecimalParameter {
  type: TypedParameter.BIGDECIMAL
  value?: number
  calculate?: boolean
}

export interface ILocalDateParameter {
  type: TypedParameter.LOCAL_DATE
  value: string
}

export interface IToggleableParameter {
  type: TypedParameter.TOGGLEABLE
  value: {
    turnedOn: boolean
  }
}

export interface IToggleableEnergyDistrictParameter {
  type: TypedParameter.TOGGLEABLE_ENERGY_DISTRICT
  value: {
    turnedOn: boolean
    value: {
      id: number
    }
  }
}

export interface IToggleablePressureRecessionParameter {
  type: TypedParameter.TOGGLEABLE_PRESSURE_RECESSION
  value: {
    turnedOn: boolean
    value: {
      generatorPower: number
      lossPortionRatio: number
      residualOutput: number
    }
  }
}

export interface IToggleableBigDecimalParameter {
  type: TypedParameter.TOGGLEABLE_BIGDECIMAL
  value: {
    turnedOn: boolean
    value: number
  }
}

export interface IToggleableIntegerParameter {
  type: TypedParameter.TOGGLEABLE_INTEGER
  value: {
    turnedOn: boolean
    value: number
  }
}

export interface IToggleableGenerationGroupParameter {
  type: TypedParameter.TOGGLEABLE_BIGDECIMAL
  value: {
    turnedOn: boolean
  }
}

export interface ILoadUnloadSpeedParameter {
  type: TypedParameter.TOGGLEABLE_BIGDECIMAL
  value: {
    turnedOn: boolean
    value: {
      loadSpeed: number
      unloadSpeed: number
    }
  }
}

export interface IToggleableRestrictionLimitParameter {
  type: TypedParameter.TOGGLEABLE_RESTRICTION_LIMIT
  value: {
    turnedOn: boolean
    value: {
      restrictions: Array<{
        code: number
        limit: number
        active: boolean
      }>
    }
  }
}

export interface IRegistryTypeParameter {
  type: TypedParameter.REGISTRY_TYPE
  value: string
}

export interface IAsyncTask {
  attemptCount: number
  createdDate: string
  id: number
  params: {
    date: string
    formula: string
    ispDate: string
    plantId: number
    stage: PlanningStage
    userDetails: {
      departmentId: number
      departmentName: string
      userFio: string
      userId: number
      username: string
    }
  }
  resultMessage: string
  status: TaskStatus
  type: {
    code: CalculationTaskType
    title: string
  }
  updatedDate: string
  userFio: string
  warnings: string[]
}

export type TAsyncPartialTask = Pick<IAsyncTask, 'status'> & {
  type: Omit<IAsyncTask['type'], 'title'>
}

export type TActions = 'CREATE' | 'UPDATE' | 'DELETE' | 'RESTORE' | 'ARCHIVE'

export type IEnrichedTabIdData<T> = T & {
  tabId?: string
}

export type Tabs<T extends string> = {
  value: T
  label: string
}[]

export type EmptyFunction<T> = () => T

export enum AbortRequestReason {
  REPEAT_REQUEST = 'REPEAT_REQUEST',
}

export enum UnsavedWarningMessages {
  LEAVE_PAGE = 'Изменения сохранены не будут. Вы действительно хотите перейти в другой раздел?',
  SWITCH_STATION = 'Изменения сохранены не будут. Вы действительно хотите перейти на другую станцию?',
}

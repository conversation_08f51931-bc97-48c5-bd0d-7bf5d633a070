import { HotTableClass } from '@handsontable/react'
import { ClickAwayListenerProps } from '@mui/material'
import { IAVRCHMSpreadsheetNestedHeaderData, IGetAVRCHMSpreadsheetCell } from 'entities/api/calcModelPage.entities.ts'
import { EmptyFunction, TaskStatus } from 'entities/shared/common.entities.ts'
import Handsontable from 'handsontable'
import { EditorType } from 'handsontable/editors'
import { CommentObject } from 'handsontable/plugins/comments'
import { RootStore } from 'stores/RootStore.ts'
import { SpreadsheetBaseProps } from 'widgets/Spreadsheet/ui/lib'

type AvrchmSpreadsheetData = SpreadsheetBaseProps<
  {
    readonly: boolean
    editor: EditorType | boolean
  },
  IGetAVRCHMSpreadsheetCell & { className: string; comment: CommentObject },
  IAVRCHMSpreadsheetNestedHeaderData
>

export interface IAvrchmSpreadsheetSelectedCell {
  row: number
  col: number
  plantId: number
}

export interface IAvrchmSpreadsheetColumn {
  columnIdx: number
  plantId: number
}

export interface IVaultSelectedCell {
  row: number
  fixed?: boolean
  idStation?: number
  keyStation?: string
}

export interface IVaultSyncTaskStatus {
  SAVE: TaskStatus
  UPDATE_ACCEPT: TaskStatus
  UPDATE_ACCEPT_CANCEL: TaskStatus
}

export interface IAvrchmStore {
  rootStore: RootStore
  // Данные по Своду в формате [{ [{plantId}-{columnKey}-{rgeIdx}]: number }]
  originalAvrchmSpreadsheet: AvrchmSpreadsheetData
  avrchmSpreadsheet: AvrchmSpreadsheetData
  avrchmSpreadsheetDataLoaded: boolean
  isChangedAvrchmSpreadsheet: boolean
  selectedCellsBeforeFix: IAvrchmSpreadsheetSelectedCell[]
  avrchmColumns: IAvrchmSpreadsheetColumn[]
  changedAvrchmCells: number[]
  isInitAvrchmSpreadsheet: boolean

  initAvrchm: (shouldChangeRequestStatus?: boolean) => Promise<void>
  resetAvrchm: EmptyFunction<void>
  _getAvrchmSpreadsheetData: (
    calcDate: string,
    planningStage?: string,
    shouldChangeRequestStatus?: boolean,
  ) => Promise<void>
  _validateAVRCHMCell: (props: IAvrchmStore['avrchmSpreadsheet']['cell'][0]) => {
    className: string
    comment: CommentObject
  }
  handleAfterChange: Handsontable.GridSettings['afterChange']
  syncWithVault: (props: [Handsontable.CellChange, number][]) => void
  applySelectedCellChanges: (fixed: boolean) => void
  changeFloodModeForAvrchmByPlantId: (plantId: number, enable: boolean) => void
  syncVaultSelectionWithAvrchm: (changedCells: IVaultSelectedCell[]) => void
  selectSpreadsheetCoords: (hot: HotTableClass | null) => void
  changeAvrchmSelection: (changedCell: IAvrchmSpreadsheetSelectedCell) => void
  resetAvrchmSelectionBeforeFix: () => void
  handleClickAway: ClickAwayListenerProps['onClickAway']
}

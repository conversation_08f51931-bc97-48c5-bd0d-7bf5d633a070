import { EmptyFunction } from 'entities/shared/common.entities.ts'
import { EditorType } from 'handsontable/editors'
import { CommentObject } from 'handsontable/plugins/comments'
import { RootStore } from 'stores/RootStore.ts'
import { SpreadsheetBaseProps } from 'widgets/Spreadsheet/ui/lib/spreadsheetResponceConverter.ts'

export interface IListOfStationsStore {
  rootStore: RootStore
  avrchmSettingsTargetDate: string | null
  avrchmSettings: ICalcModelAvrchmSettings
  isAvrchmSettingsChanged: boolean
  editableAvrchmSetting: IEditableAvrchmSetting | undefined
  avrchmSpreadsheet: SpreadsheetBaseProps<
    {
      readOnly: boolean
      editor: EditorType | boolean
    },
    {
      className: string
    }
  >
  avrchmSpreadsheetDataLoaded: boolean
  initAvrchmSettings: (targetDate: string) => Promise<void>
  saveAvrchmSettings: EmptyFunction<Promise<void>>
  resetAvrchmSettings: EmptyFunction<void>
  addAvrchmSettingsColumn: (column: ICalcModelAvrchmSettings['columns'][0]) => void
  editAvrchmSettingsColumn: (
    idx: number,
    action: 'start' | 'apply' | 'end',
    column?: ICalcModelAvrchmSettings['columns'][0],
  ) => void
  _getAvrchmSpreadsheetData: (calcDate: string) => Promise<void>
  _validateAVRCHMCell: (props: IListOfStationsStore['avrchmSpreadsheet']['cell'][0]) => {
    className: string
    comment: CommentObject
  }
  moveAvrchmSettingsColumn: (idx: number, direction: 'left' | 'right') => void
  removeAvrchmSettingsColumn: (idx: number) => void
}

export interface IEditableAvrchmSetting {
  idx: number
  avrchmSetting: ICalcModelAvrchmSettings['columns'][0]
}

export interface StagesProps {
  title: string
  code: string
  color: string
}

export type TPlant = 'GES' | 'GAES'

export interface ICalcModelPlantListItem extends ICalcModelPlant {
  value: number // Идентификатор станции
  label: string // Название станции
  icon: 'view' | 'settings'
  plantType: TPlant
}

export interface ICalcModelPlant extends IPlant {
  viewOnly: boolean // Флаг просмотра (только для чтения)
  mixing: boolean // Флаг возможности миксирования
  accepted: boolean // Флаг акцепта
  name: string
  type: TPlant
}

export interface IPlant {
  plantId: number // Идентификатор станции
  name: string // Название станции
}

export interface IGetEnergyDistrictOutput {
  id: number
  ispId: number
  name: string
  used: boolean
  archived: boolean
  value: number | string
}

export enum CalcModelAvrchmSettingsSource {
  NEPTUNE = 'NEPTUNE',
  MODES = 'MODES',
  MODES_TES = 'MODES_TES',
  FORMULA = 'FORMULA',
}

export enum CalcModelAvrchmSettingsSourceName {
  NEPTUNE = 'Нептун',
  MODES = 'Модес',
  MODES_TES = 'Модес (ТЭС)',
  FORMULA = 'Формула',
}

export type ICalcModelAvrchmSettingsColumn = keyof ICalcModelAvrchmSettings['columns'][0]

export interface ICalcModelAvrchmSettings {
  columns: {
    title: string
    source: CalcModelAvrchmSettingsSource
    plant?: {
      plantId: number
      name: string
    }
    marketCalcModelId?: number
    formula?: string
    influence?: number
    minNorm?: number
  }[]
}

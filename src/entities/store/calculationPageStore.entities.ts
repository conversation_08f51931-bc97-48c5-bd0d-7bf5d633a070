import {
  IAllowedZone,
  ICalcPossibility,
  ICalculationRow,
  ICalculationSummary,
  IHourAllowedZones,
  IInputValues,
  IRguCalculation,
  ITelemetryOutput,
  StageType,
} from 'entities/api/calculationsManager.entities.ts'
import { TStatus } from 'entities/api/nsiManager.entities.ts'
import { IStage } from 'entities/pages/calcPage.entities.tsx'
import {
  CalculationInput,
  IBigDecimalParameter,
  ILocalDateParameter,
  IRegistryTypeParameter,
  IToggleableBigDecimalParameter,
  PlanningStage,
  PlantType,
  Protocol,
  TaskStatus,
} from 'entities/shared/common.entities'
import { TSort } from 'pages/CalcModelPage/ui/ui/ListOfStations'
import { ITelemetryRowItem } from 'shared/lib/prepareTelemetryTableData'
import { IPlantForLeftMenu } from 'stores/CalcModelStore'
import { AvrchmStore } from 'stores/CalculationsPageStore/AvrchmStore.ts'
import { VaultStore } from 'stores/CalculationsPageStore/VaultStore/VaultStore.ts'
import { RootStore } from 'stores/RootStore.ts'

export enum ActualPlanningStage {
  ACTUAL = 'ACTUAL',
}

interface IStoredStage extends Omit<IStage, 'value'> {
  value:
    | PlanningStage.PER
    | PlanningStage.PDG
    | PlanningStage.VSVGO1
    | PlanningStage.VSVGO2
    | PlanningStage.VSVGO3
    | ActualPlanningStage.ACTUAL
}

interface IWMin extends IBigDecimalParameter {
  calculate?: boolean
}

interface IDataForStationInputValue extends Omit<IInputValues, CalculationInput.W_MIN> {
  [CalculationInput.W_MIN]?: IWMin
}

interface IDataForStation {
  inputValues: IDataForStationInputValue | Record<string, never>
  planingStage: PlanningStage | null
  plantId: number | null
  plantType: PlantType | null
  rows: ICalculationRow[]
  rgus: IRguCalculation[]
  allowedZones: IHourAllowedZones[]
  maxConsumptionHour: number
  minConsumptionHour: number
  plantOptimized: boolean
  REGULATED_UNIT: IRegistryTypeParameter['value'] | null
  E_MAX_E_MIN: number | null
  accepted: boolean
  targetDate: string
  emaxemin: IToggleableBigDecimalParameter['value'] | null
  ISP_DATE: ILocalDateParameter['value'] | null
  columnStages: Record<string, string>
  needInit?: boolean
}

export enum StagesInitStatus {
  idle,
  loading,
  loadedWithData,
  loadedWithoutData,
}

interface IVaultDataItem {
  tabId: string
  hour: string
  [key: string]: number | string | IAllowedZone[]
}

export interface IStationSyncTaskStatus {
  SAVE: TaskStatus
  UPDATE_ACCEPT: TaskStatus
  UPDATE_ACCEPT_CANCEL: TaskStatus
  INITIALIZE: TaskStatus
}

export interface IAcceptErrorResponse {
  rid: string
  status: number
  message: string
  details: string[]
}

export interface ICalculationsPageStore {
  rootStore: RootStore
  avrchmStore: AvrchmStore
  vaultStore: VaultStore
  stages: IStoredStage[]
  acceptedErrors: string[]
  isLoadingReportDailyOutput: boolean | null
  reportDailyOutput: Protocol
  dataForStation: IDataForStation
  summaryValues: any
  updateStation: {
    plantId: number
    date: string
    stage: IStage
  } | null
  plantsData: ICalculationSummary['plants']
  vaultData: IVaultDataItem[]
  actualStage: StageType | null
  currentPlantId: number | null
  mode: string | null
  selectedStage: string | null
  isLoadingInfoStation: boolean
  isLoadingSourceData: boolean
  isLoadingTelemetry: boolean
  loadingTelemetryByDateStatus: TaskStatus
  telemetry: ITelemetryOutput | null
  telemetryTableData: ITelemetryRowItem[]
  telemetryChartActiveLegendItems: string[]
  loadStages: boolean
  calcPossibility: ICalcPossibility | null
  isLoadingAvrchmTes: boolean
  isLoadingTelemetryTable: boolean
  telemetryTableDate: string | null
  isStagesExist: StagesInitStatus
  calculationXlsExportStatus: TStatus
  syncTaskStatus: IStationSyncTaskStatus
  plantsListForAside: IPlantForLeftMenu[]
  typeSort: TSort

  handleClickOnTelemetryChartLegendItems: (legendItems: string) => void
  setSyncStatus: (key: keyof IStationSyncTaskStatus, taskStatus: TaskStatus) => void
  setPlantsListForAside: (plantsListForAside: IPlantForLeftMenu[]) => void
}

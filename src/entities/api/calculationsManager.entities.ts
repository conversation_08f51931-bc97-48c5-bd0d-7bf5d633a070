import {
  CalculationColumn,
  CalculationInput,
  IBigDecimalParameter,
  ILoadUnloadSpeedParameter,
  ILocalDateParameter,
  IRegistryTypeParameter,
  IToggleableBigDecimalParameter,
  IToggleableEnergyDistrictParameter,
  IToggleableGenerationGroupParameter,
  IToggleableIntegerParameter,
  IToggleableParameter,
  IToggleablePressureRecessionParameter,
  IToggleableRestrictionLimitParameter,
  PlanningStage,
  PlantParameter,
  PlantType,
  RegistryType,
  SummaryColumn,
  TaskStatus,
} from 'entities/shared/common.entities.ts'
import { IGetSpreadsheetData } from 'entities/shared/spreadsheetDataResponse.ts'
import { MessagesWarnings } from 'entities/widgets/Vault.entities.ts'
import { IStageType } from 'shared/api/reportsManager/reportsManager'

export type IHourItem = number | null | undefined

interface ITelemetryItem {
  /** Время измерения телеметрии */
  dateTime: string
  /** Измерение телеметрии */
  value: number
}

export interface ILoadTelemetryOutput {
  /** Id задачи */
  id: number
  /** Тип задачи */
  type: {
    /** Обозначение типа задачи */
    code: string
    /** Название типа задачи */
    title: string
  }
  /** Статус задачи */
  status: TaskStatus
  /** Кем создана задача */
  userFio: string
  /** Дата создания задачи */
  createdDate: string
  /** Дата обновления задачи */
  updatedDate: string
  /** Кол-во попыток */
  attemptCount: number
  /** Параметры задачи */
  params: {
    date: string
    plantId: number
    userDetails: {
      username: string
      userId: number
      userFio: string
      departmentId: number
      departmentName: string
    }
  }
}

export interface ITelemetryHourItem {
  title: string
  values: IHourItem[]
  maxHour: number
  minHour: number
}

export interface ITelemetrySpreadsheetCell {
  maxHour: boolean
  minHour: boolean
  value: number
}

export interface ITelemetryOutput {
  plantId: number
  targetDate: string
  hourTable?: IGetSpreadsheetData<ITelemetrySpreadsheetCell>
  hourItems?: Array<ITelemetryHourItem>
  minuteItems: Array<{
    title: 'вб' | 'нб' | 'напор' | 'Факт' | 'ПБР'
    values: ITelemetryItem[]
  }>
}

export interface StageType {
  code: PlanningStage
  title: string
  finished: boolean
}
export interface IStagesTypeOutput {
  stages: StageType[]
  actualStage: StageType
}
export interface StagesParamsType {
  calcDate: string
}

export interface IListStagesOutput {
  dateStages: {
    [date: string]: {
      stages: IStageType[]
      actualStage: Omit<IStageType, 'finished'>
    }
  }
}

export interface ICalculationLoadItemItem {
  id: number
  type: {
    code: string
    title: string
  }
  status: TaskStatus
  userFio: string
  createdDate: string
  updatedDate: string
  attemptCount: number
  resultMessage: string
  params: object
}

export type ICalculationLoadOutput = ICalculationLoadItemItem[]

export interface IDownloadAvrchmTesInput {
  targetDate: string
  planingStage: PlanningStage
}

type IRguValue = {
  /** Идентификатор станции */
  plantId: number
  /** Идентификатор РГЕ */
  rguId: number
  /** Список сохраняемых значений таблицы */
  values: ICalculationValue[]
}

export interface ISaveDataStationInput {
  /** Дата расчёта (2023-07-17) */
  targetDate: string
  /** Код этапа планирования */
  planingStage: PlanningStage
  /** Идентификатор станции */
  plantId: number
  /** Список сохраняемых значений станции таблицы */
  values: ICalculationValue[]
  /** Входные параметры для расчёта */
  inputValues: IInputValues
  /** Список сохраняемых значений РГЕ таблицы */
  rguValues: IRguValue[]
}

export interface ICalculationValue {
  /** Обозначение столбца */
  column: CalculationColumn
  /** Номер часа */
  hour: number
  /** Значение */
  value: number | null
  /** Признак ручного ввода */
  manual?: boolean
  /** Признак фиксации значения */
  fixed?: boolean
}

export interface ICalculationRow {
  /** Номер часа */
  hour: number
  /** Ячейки со значениями */
  cells: ICalculationValue[]
}

export interface IAllowedZone {
  /** Нижнее значение разрешённой зоны */
  bottomLine: number
  /** Верхнее значение разрешённой зоны */
  topLine: number
}

export interface IHourAllowedZones {
  /** Номер часа */
  hour: number
  /** Список допустимых зон */
  zones: IAllowedZone[]
}

export interface IRguCalculation {
  /** Идентификатор РГЕ */
  rguId: number
  /** Название РГЕ */
  rguName: string
  /** Строки со значениями расчёта */
  rows: ICalculationRow[]
  /** Списки допустимых зон по часам */
  allowedZones: IHourAllowedZones[]
  /** */
  columnStages: ICalculationSummaryItem['columnStages']
}

export interface IInputValues {
  /** Входные значения с типами */
  [CalculationInput.ISP_DATE]?: ILocalDateParameter
  [CalculationInput.P_GEN_TARGET]?: IBigDecimalParameter
  [CalculationInput.FLOOD_MODE_WATCH]?: IToggleableParameter
  [CalculationInput.W_MIN]: IBigDecimalParameter
  [CalculationInput.W_MAX]?: IBigDecimalParameter
  [CalculationInput.TERTIARY_RESERVE]: IBigDecimalParameter
}

export interface ICalculation {
  /** Идентификатор станции */
  plantId: number
  /** Тип станции */
  plantType: PlantType
  /** Название станции */
  plantName: string
  /** Признак оптимизации */
  plantOptimized: boolean
  /** Регулируемая единица */
  regulatedUnit: RegistryType
  /** ID рыночной РМ станции */
  plantMarketCalcModelId?: number
  /** Дата, на которую произведён расчёт */
  targetDate: string
  /** Этап планирования */
  planingStage: PlanningStage
  /** Признак акцепта */
  accepted: boolean
  /** Минимальный час потребления */
  minConsumptionHour: number
  /** Максимальный час потребления */
  maxConsumptionHour: number
  /** Входные значения с типами */
  inputValues: IInputValues
  /** Характеристики станции */
  parameters: {
    [PlantParameter.CONSUMPTION_SCHEDULE_BINDING]?: IToggleableEnergyDistrictParameter
    [PlantParameter.REGULATED_UNIT]?: IRegistryTypeParameter
    [PlantParameter.OPTIMIZATION]?: IToggleableParameter
    [PlantParameter.EFFICIENCY]?: IBigDecimalParameter
    [PlantParameter.PARTICIPATION_NPRCH]?: IToggleableBigDecimalParameter
    [PlantParameter.PARTICIPATION_AVRCHM]?: IToggleableBigDecimalParameter
    [PlantParameter.GENERATOR_JOINT_WORK_WATCH]?: IToggleableGenerationGroupParameter
    [PlantParameter.MINIMUM_LIMIT]?: IToggleableRestrictionLimitParameter
    [PlantParameter.MAXIMUM_LIMIT]?: IToggleableRestrictionLimitParameter
    [PlantParameter.PRESSURE_RECESSION_WATCH]?: IToggleablePressureRecessionParameter
    [PlantParameter.RGU_GROUP]?: IToggleableIntegerParameter
    [PlantParameter.E_MAX_E_MIN]?: IToggleableBigDecimalParameter
    [PlantParameter.LOAD_UNLOAD_SPEED]?: ILoadUnloadSpeedParameter
    [PlantParameter.FLOOD_MODE_WATCH]?: IToggleableParameter
  }
  /** Расчёты по РГЕ */
  rgus: IRguCalculation[]
  /** Строки со значениями расчёта */
  rows: ICalculationRow[]
  /** Списки допустимых зон по часам */
  allowedZones: IHourAllowedZones[]
  /** */
  columnStages: Record<string, string>
  // Опциональный параметр, сигнализирующий о необходимости переинициализации расчёта
  needInit?: boolean
}

export interface ICalculationSummaryItem {
  /** Идентификатор станции */
  plantId: number
  /** Тип станции */
  plantType: PlantType
  /** Название станции */
  planTitle: string
  /** Признак оптимизации */
  plantOptimized: boolean
  /** Регулируемая единица */
  regulatedUnit: RegistryType
  /** Признак акцепта */
  accepted: boolean
  /** Флаг необходимости отображения станции в таблице АВРЧМ */
  showInAvrchm: boolean
  /** Минимальный час потребления */
  minConsumptionHour: number
  /** Максимальный час потребления */
  maxConsumptionHour: number
  /** Строки со значениями */
  rows: ICalculationRow[]
  /** Расчёты по РГЕ */
  rgus: IRguCalculation[]
  /** Входные значения с типами */
  inputValues: IInputValues
  /** Списки допустимых зон по часам */
  allowedZones: IHourAllowedZones[]
  /** Характеристики станции */
  parameters: ICalculation['parameters']
  /** Этапы планирования, в которых менялись данные в колонках */
  columnStages: {
    [CalculationColumn.CONSUMPT]: PlanningStage
    [CalculationColumn.MODES_P_MAX]: PlanningStage
    [CalculationColumn.MODES_P_MIN]: PlanningStage
    [CalculationColumn.MODES_DECLARED]: PlanningStage
    [CalculationColumn.P_GEN]: PlanningStage
    [CalculationColumn.NPRCH]: PlanningStage
    [CalculationColumn.AVRCHM_LOAD]: PlanningStage
    [CalculationColumn.AVRCHM_UNLOAD]: PlanningStage
    [CalculationColumn.CM_P_MAX]: PlanningStage
    [CalculationColumn.CM_P_MIN]: PlanningStage
    [CalculationColumn.LIMIT_MAX]: PlanningStage
    [CalculationColumn.LIMIT_MIN]: PlanningStage
    [CalculationColumn.P_SOURCE]: PlanningStage
    [CalculationColumn.RESERVES_MAX]: PlanningStage
    [CalculationColumn.RESULT_MAX]: PlanningStage
    [CalculationColumn.RESULT_MIN]: PlanningStage
  }
}

export interface ISummaryValue {
  /** Обозначение характеристики */
  column: SummaryColumn
  /** Номер часа */
  hour: number
  /** Значение */
  value: number
  /** Идентификатор рыночной расчётной модели станции в Модесе */
  marketCalcModelId: number
}

export interface ICalculationSummary {
  /** Дата, на которую произведён расчёт */
  targetDate: string
  /** Этап планирования */
  planingStage: PlanningStage
  /** Данные по станциям */
  plants: ICalculationSummaryItem[]
}

export interface ISaveDataVaultInput {
  /** Дата расчёта */
  targetDate: string
  /** Код этапа планирования */
  planingStage: PlanningStage
  /** Идентификаторы станций, по умолчанию - все доступные станции на выбранную дату */
  calculationValues: {
    plantId: number
    values: ICalculationValue[]
  }[]
  /** Список сохраняемых значений РГЕ таблицы */
  rguValues: IRguValue[]
  /** Список входных параметров по станциям */
  inputValues: {
    plantId: number
    values: {
      W_MAX?: IBigDecimalParameter
      P_GEN_TARGET?: IBigDecimalParameter
      W_MIN?: IBigDecimalParameter
      FLOOD_MODE_WATCH?: IToggleableParameter
      ISP_DATE?: ILocalDateParameter
    }
  }[]
}

export type TCalcGenerationMethod = 'DIVING_BELL' | 'MAXIMUM'

export interface IBaseCalcInput {
  targetDate: string
  planingStage: PlanningStage
  plantId: number
  method?: TCalcGenerationMethod
}

export enum AllowedZonedMode {
  CONSUMPTION_SCHEDULE_CHANGE = 'CONSUMPTION_SCHEDULE_CHANGE',
  ROUND_TO_BOUND = 'ROUND_TO_BOUND',
}

export interface ICalcPossibility {
  canUpdateFix: boolean
  canCalculateAllowedZones: boolean
  canCalculateGeneration: boolean
  canCalculateGenerationMaximum: boolean
  canEnteringAllowedZones: boolean
  canEnteringAllowedZonesToBounds: boolean
  canWriteModes: boolean
  canBalanceRgu: boolean
  canDoOptimization: boolean
  canLoadAll: boolean
  canLoadIsp: boolean
  canLoadModes: boolean
  canLoadCm: boolean
  canLoadAvrchm: boolean
  canInitialize: boolean
  canUpdateAccept: boolean
  canSendReport: boolean
  warnings: any
  taskStatus: {
    [MessagesWarnings.WRITE_MODES]: TaskStatus
    [MessagesWarnings.DO_OPTIMIZATION]: TaskStatus
    [MessagesWarnings.LOAD_ISP]: TaskStatus
    [MessagesWarnings.LOAD_MODES]: TaskStatus
    [MessagesWarnings.LOAD_CM]: TaskStatus
    [MessagesWarnings.CALCULATE_ALLOWED_ZONES]: TaskStatus
    [MessagesWarnings.CALCULATE_GENERATION]: TaskStatus
    [MessagesWarnings.CALCULATE_GENERATION_MAXIMUM]: TaskStatus
    [MessagesWarnings.ENTERING_ALLOWED_ZONES]: TaskStatus
    [MessagesWarnings.ENTERING_ALLOWED_ZONES_TO_BOUNDS]: TaskStatus
    [MessagesWarnings.BALANCE_RGU]: TaskStatus
  }
}

export interface IGetCalculationXlsInput {
  // Идентификатор станции
  plantId: number
  // Дата расчёта
  targetDate: string
  // Этап планирования
  planingStage: PlanningStage
  // Колонки для экспорта
  columns: Array<
    | 'P_MIN_RESULT'
    | 'P_GEN'
    | 'P_MAX_RESULT'
    | 'P_SOURCE'
    | 'RESERVE_MAX'
    | 'AVRCHM_LOAD'
    | 'AVRCHM_UNLOAD'
    | 'LIMIT_MIN'
    | 'LIMIT_MAX'
    | 'CM_P_MIN'
    | 'CM_P_MAX'
    | 'MODES_P_MIN'
    | 'MODES_P_MAX'
    | 'MODES_DECLARED'
    | 'CONSUMPT'
    | 'RESULT_MIN'
    | 'RESULT_MAX'
    | 'RESERVES_MAX'
  >
  // Флаг экспорта данных РГЕ (опционально)
  rguData?: boolean
}

export interface IWarningOutput {
  warnings: string[]
}

export type TStatusUpdateModalVault = 'IN_PROCESS' | 'DONE' | 'FAILED' | 'NOT_EXECUTED' | 'WARNING' | 'DONE_WITH_ERRORS'

export interface IGetDataUpdateVaultInput {
  calcDate: string
  planingStage?: string | null
}

export interface IGetDataUpdateVaultOutput {
  plantId: number
  plantName: string
  type: {
    code: string
    title: string
  }
  status: TStatusUpdateModalVault
  userFio?: string
  updatedDate?: string
  warnings?: string[]
}

export interface IVaultActionInput {
  targetDate: string
  planingStage: string
  plantIds: number[]
  mode?: AllowedZonedMode
  method?: TCalcGenerationMethod
}

export interface IVaultActionOutput {
  warnings?: string[]
}

export interface IAcceptVault {
  targetDate: string
  planingStage: string
  plantIds: number[]
}

export interface IAcceptVaultOutput {
  plantId: number
  name: string
  success: boolean
  errors: string[]
}

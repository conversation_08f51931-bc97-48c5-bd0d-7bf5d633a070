import { TPlant } from 'entities/store/calcModelStore.entities'

export type TGetRegistryInput = 'BY_GENERATOR' | 'BY_RGU'

export interface IPlanDepartment {
  id: number
  uid: string
  parentId: number
  name: string
  value: string
}

export interface IGetRegistryOutput {
  type: string
  id: number
  uid: string
  name: string
  startDate: string
  endDate: string
  planDepartments: IPlanDepartment[]
  marketCalcModelId: number
  active: boolean
  children: IPlanDepartment[]
  plantType: TPlant
}

export interface ISaveRegistry {
  type: string
  id: number
  startDate: string
  endDate: string
  planDepartmentIds: number[]
  marketCalcModelId: number
}

export type getLastTaskSK11TaskType =
  | 'SYNC_REGISTRY'
  | 'COMPARE_PLANTS'
  | 'APPLY_PLANTS'
  | 'EDIT_REGISTRY'
  | 'SYNC_REFERENCE_DATA'

export type TStatus = 'IN_PROCESS' | 'DONE' | 'FAILED'

export interface IGetLastTaskSK11OutputSync {
  status: TStatus
  finishedDate: string
  activeModelVersion: number
}

export interface IGetLastTaskSK11Output {
  id: number
  type: {
    code: string
    title: string
  }
  status: TStatus
  userFio: string
  createdDate: string
  updatedDate: string
  attemptCount: number
  resultMessage: string
  params: object
  finishedDate: string
  activeModelVersion: number
}

export interface IPlantsSK11 {
  type: string
  uid: string
  startDate: string
  endDate: string
  planDepartmentIds: number[]
  marketCalcModelId: number
}

export interface ISaveStationSK11Input {
  modelVersion: number
  plants?: IPlantsSK11[]
}

export interface ISaveStationSK11Output {
  modelVersion: number
  results: [
    {
      type: string
      added: number
      changed: number
      deleted: number
      warnings: string[]
    },
  ]
}

export interface IStartSK11Output {
  id: number
  type: {
    code: string
    title: string
  }
  status: 'IN_PROCESS'
  userFio: string
  createdDate: string
  updatedDate: string
  attemptCount: number
  resultMessage: string
  params: unknown
}

export interface ILoadSK11ReferenceData extends IStartSK11Output {
  warnings: string[]
}

export interface IEditRestrictionCausesOutput {
  action: string
  restriction: {
    type?: string
    code?: number
    name?: string
    allowArchive: boolean
    allowDelete: boolean
    archived: boolean
  }
}

export interface IGetEnergyDistrictOutput {
  id: number
  ispId: number
  name: string
  archived: boolean
  allowArchive: boolean
  allowDelete: boolean
}

export interface IEditEnergyDistrictOutput {
  action: string
  archived: boolean
  district: {
    id: number
    ispId: number
    name: string
    archived: boolean
    allowArchive: boolean
    allowDelete: boolean
  }
}

export interface IValidateSyncInputPlant {
  type: string
  uid: string
  startDate: string
  endDate: string
  planDepartmentIds: number[]
  marketCalcModelId: number
}

export interface IValidateSyncInput {
  modelVersion?: number
  plants?: IValidateSyncInputPlant[]
}

export interface IValidateSyncOutput {
  errors: {
    uid: string
    field: string
    message: string
  }[]
}

export interface IMaxCode {
  MAXIMUM: number
  MINIMUM: number
}

export interface IGetRestrictionCauseOutput {
  type: string
  code: number
  name: string
  allowArchive: boolean
  allowDelete: boolean
  archived: boolean
  action: string
  id: number
  ispId: number
}

export type TPlantsDiffType = 'DELETED' | 'CHANGED' | 'ADDED' | 'UNCHANGED' | 'EARLY_ADDED'

export interface IPlantsDiff {
  type: TPlantsDiffType
  id: number
  uid: string
  name: string
  department: {
    id: number
    uid: string
    parentId: number
    name: string
  }
  startDate: string
  endDate: string
  planDepartments: [
    {
      id: number
      uid: string
      parentId: number
      name: string
    },
  ]
  werDepartments: [
    {
      id: number
      value: number
      uid: string
      parentId: number
      name: string
      label: string
    },
  ]
  marketCalcModelId: number
  updatedFields: [
    {
      parameter: string
      oldValue: string
      newValue: string
    },
  ]
  plantType: TPlant
}

export interface IGetStationSK11Output {
  modelVersion: number
  loadDate: string
  plantsDiff: IPlantsDiff[]
}

export interface IGetCascades {
  id: number
  name: string
  archived: boolean
  allowArchive: boolean
  allowDelete: boolean
  plants: {
    plantId: number
    name: string
    active: boolean
  }[][]
}

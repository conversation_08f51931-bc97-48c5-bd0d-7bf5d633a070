import { PlanningStage, PlanningStageRussia, TaskStatus } from 'entities/shared/common.entities'

type StageCode = keyof typeof PlanningStage
type StageTitle = `${PlanningStageRussia}`

type DateString = `${number}${number}${number}${number}-${number}${number}-${number}${number}`

interface CalculationStage {
  code: StageCode
  title: StageTitle
  finished: boolean
}

export interface ILastStageForDates {
  [date: DateString]: CalculationStage
}

export interface IGAESSpreadsheetCell {
  editable?: boolean
  hour?: number
  column?: string
  value?: number | string | null
  calcDate?: string
  manual?: boolean
  input?: string
  fixed?: boolean
}

export type TTaskType = 'LOAD_CONSUMPTION' | 'LOAD_PLANT_DATA'

export interface ICalculationLoadItemItem {
  id: number
  type: {
    code: string
    title: string
  }
  status: TaskStatus
  userFio: string
  createdDate: string
  updatedDate: string
  attemptCount: number
  resultMessage: string
  params: object
}

export type ICalculationLoadOutput = ICalculationLoadItemItem[]

export interface ICalculationHistoryAcceptItem {
  updatedDate: string
  accepted: boolean
  userName: string
  departmentName: string
  targetDate: string
  stage: StageTitle
}

export type ICalculationHistoryAcceptOutput = ICalculationHistoryAcceptItem[]

export interface IGAESXlsReportParams {
  plantId: number
  dates: string[]
  columns: Array<'P_GEN' | 'MODES_P_MIN' | 'MODES_P_MAX' | 'CONSUMPT'>
  prevDates?: string[]
}
